import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  ValidationPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DialogueService } from './dialogue.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { CreateSessionResponseDto } from './dto/create-session-response.dto';
import { SendMessageResponseDto } from './dto/send-message-response.dto';
import { SessionHistoryResponseDto } from './dto/session-history-response.dto';
import { SessionListResponseDto } from './dto/session-list-response.dto';

@ApiTags('dialogue')
@Controller('dialogue')
export class DialogueController {
  private readonly logger = new Logger(DialogueController.name);

  constructor(private readonly dialogueService: DialogueService) {}

  @Post('sessions')
  @ApiOperation({ summary: '创建对话会话' })
  @ApiBody({ type: CreateSessionDto })
  @ApiResponse({
    status: 201,
    description: '会话创建成功',
    type: CreateSessionResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createSession(
    @Body(ValidationPipe) createDto: CreateSessionDto,
  ): Promise<CreateSessionResponseDto> {
    try {
      this.logger.log(`创建对话会话: ${JSON.stringify(createDto)}`);
      
      const session = await this.dialogueService.createSession(createDto);
      
      return {
        success: true,
        data: {
          sessionId: session.id,
          sceneId: session.sceneId,
          avatarId: session.avatarId,
          knowledgeBaseId: session.knowledgeBaseId,
          status: session.status,
          createdAt: session.createdAt,
        },
        message: '会话创建成功',
      };
    } catch (error) {
      this.logger.error(`创建会话失败: ${error.message}`, error.stack);
      throw new HttpException(
        `创建会话失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('sessions/:sessionId/messages')
  @ApiOperation({ summary: '发送消息' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiBody({ type: SendMessageDto })
  @ApiResponse({
    status: 200,
    description: '消息发送成功',
    type: SendMessageResponseDto,
  })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async sendMessage(
    @Param('sessionId') sessionId: string,
    @Body(ValidationPipe) messageDto: SendMessageDto,
  ): Promise<SendMessageResponseDto> {
    try {
      this.logger.log(`发送消息到会话 ${sessionId}: ${messageDto.content}`);
      
      const response = await this.dialogueService.processMessage(sessionId, messageDto);
      
      return {
        success: true,
        data: response,
        message: '消息处理成功',
      };
    } catch (error) {
      this.logger.error(`处理消息失败: ${error.message}`, error.stack);
      throw new HttpException(
        `处理消息失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('sessions/:sessionId/history')
  @ApiOperation({ summary: '获取会话历史' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 20 })
  @ApiResponse({
    status: 200,
    description: '获取历史成功',
    type: SessionHistoryResponseDto,
  })
  async getSessionHistory(
    @Param('sessionId') sessionId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ): Promise<SessionHistoryResponseDto> {
    try {
      this.logger.log(`获取会话历史: ${sessionId}, page: ${page}, limit: ${limit}`);
      
      const history = await this.dialogueService.getSessionHistory(
        sessionId,
        Number(page),
        Number(limit),
      );
      
      return {
        success: true,
        data: history,
        message: '获取历史成功',
      };
    } catch (error) {
      this.logger.error(`获取会话历史失败: ${error.message}`, error.stack);
      throw new HttpException(
        `获取会话历史失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete('sessions/:sessionId')
  @ApiOperation({ summary: '结束会话' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '会话结束成功' })
  async endSession(@Param('sessionId') sessionId: string) {
    try {
      this.logger.log(`结束会话: ${sessionId}`);
      
      await this.dialogueService.endSession(sessionId);
      
      return {
        success: true,
        message: '会话结束成功',
      };
    } catch (error) {
      this.logger.error(`结束会话失败: ${error.message}`, error.stack);
      throw new HttpException(
        `结束会话失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
