import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { OpenAI } from 'openai';
import { DialogueSession } from './entities/dialogue-session.entity';
import { DialogueMessage } from './entities/dialogue-message.entity';
import { IntentService } from '../intent/intent.service';
import { EmotionService } from '../emotion/emotion.service';

export interface DialogueContext {
  sessionId: string;
  knowledgeBaseId: string;
  avatarId: string;
  userId?: string;
  personality?: string;
  responseStyle?: string;
  history: DialogueMessage[];
}

export interface DialogueResponse {
  text: string;
  intent: {
    type: string;
    confidence: number;
    entities?: any[];
  };
  emotion: {
    type: string;
    intensity: number;
  };
  sources: any[];
  confidence: number;
  responseTime: number;
}

interface CreateSessionDto {
  sceneId: string;
  avatarId: string;
  knowledgeBaseId: string;
  userId?: string;
  personality?: string;
  responseStyle?: string;
}

interface SendMessageDto {
  content: string;
  type: 'text' | 'audio';
  audioData?: string; // base64编码的音频数据
}

@Injectable()
export class DialogueService {
  private openai: OpenAI;

  constructor(
    @InjectRepository(DialogueSession)
    private sessionRepository: Repository<DialogueSession>,
    @InjectRepository(DialogueMessage)
    private messageRepository: Repository<DialogueMessage>,
    private configService: ConfigService,
    private httpService: HttpService,
    private intentService: IntentService,
    private emotionService: EmotionService,
    private eventEmitter: EventEmitter2,
  ) {
    // 初始化OpenAI客户端
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
      baseURL: this.configService.get<string>('OPENAI_BASE_URL'),
    });
  }

  /**
   * 创建对话会话
   */
  async createSession(createDto: CreateSessionDto): Promise<DialogueSession> {
    const session = this.sessionRepository.create({
      sceneId: createDto.sceneId,
      avatarId: createDto.avatarId,
      knowledgeBaseId: createDto.knowledgeBaseId,
      userId: createDto.userId,
      status: 'active',
      metadata: {
        personality: createDto.personality || 'friendly',
        responseStyle: createDto.responseStyle || 'professional',
        createdAt: new Date().toISOString(),
      },
    });

    const savedSession = await this.sessionRepository.save(session);

    // 发送会话创建事件
    this.eventEmitter.emit('session.created', {
      sessionId: savedSession.id,
      sceneId: createDto.sceneId,
      avatarId: createDto.avatarId,
      userId: createDto.userId,
    });

    return savedSession;
  }

  /**
   * 处理消息
   */
  async processMessage(
    sessionId: string,
    messageDto: SendMessageDto,
  ): Promise<DialogueResponse> {
    const startTime = Date.now();

    // 获取会话
    const session = await this.getSession(sessionId);
    
    // 保存用户消息
    const userMessage = await this.saveMessage(sessionId, {
      role: 'user',
      content: messageDto.content,
      type: messageDto.type,
      audioData: messageDto.audioData,
    });

    try {
      // 1. 意图理解
      const intent = await this.intentService.analyzeIntent(messageDto.content);

      // 2. 情感分析
      const emotion = await this.emotionService.analyzeEmotion(messageDto.content);

      // 3. 知识检索
      const searchResults = await this.searchKnowledgeBase(
        session.knowledgeBaseId,
        messageDto.content,
        { topK: 5, threshold: 0.7 }
      );

      // 4. 构建对话上下文
      const context = await this.buildDialogueContext(session);

      // 5. 生成回答
      const response = await this.generateResponse(
        messageDto.content,
        searchResults,
        context,
        intent,
        emotion
      );

      // 6. 保存助手消息
      await this.saveMessage(sessionId, {
        role: 'assistant',
        content: response.text,
        type: 'text',
        metadata: {
          intent,
          emotion: response.emotion,
          sources: response.sources,
          confidence: response.confidence,
        },
      });

      // 7. 更新会话
      await this.updateSessionActivity(sessionId);

      const responseTime = Date.now() - startTime;

      // 发送消息处理事件
      this.eventEmitter.emit('message.processed', {
        sessionId,
        userMessage: userMessage.content,
        response: response.text,
        intent,
        emotion: response.emotion,
        responseTime,
      });

      return {
        ...response,
        responseTime,
      };

    } catch (error) {
      console.error('处理消息失败:', error);
      
      // 保存错误消息
      await this.saveMessage(sessionId, {
        role: 'assistant',
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        type: 'text',
        metadata: {
          error: error.message,
        },
      });

      throw new BadRequestException(`处理消息失败: ${error.message}`);
    }
  }

  /**
   * 搜索知识库
   */
  private async searchKnowledgeBase(
    knowledgeBaseId: string,
    query: string,
    options: { topK: number; threshold: number }
  ): Promise<any[]> {
    try {
      const knowledgeBaseUrl = this.configService.get<string>('KNOWLEDGE_BASE_SERVICE_URL');
      
      const response = await firstValueFrom(
        this.httpService.post(`${knowledgeBaseUrl}/api/v1/knowledge-base/${knowledgeBaseId}/search`, {
          query,
          topK: options.topK,
          threshold: options.threshold,
        })
      );

      return response.data.results || [];
    } catch (error) {
      console.error('知识库搜索失败:', error);
      return [];
    }
  }

  /**
   * 构建对话上下文
   */
  private async buildDialogueContext(session: DialogueSession): Promise<DialogueContext> {
    // 获取最近的对话历史
    const recentMessages = await this.messageRepository.find({
      where: { sessionId: session.id },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    return {
      sessionId: session.id,
      knowledgeBaseId: session.knowledgeBaseId,
      avatarId: session.avatarId,
      userId: session.userId,
      personality: session.metadata?.personality || 'friendly',
      responseStyle: session.metadata?.responseStyle || 'professional',
      history: recentMessages.reverse(),
    };
  }

  /**
   * 生成回答
   */
  private async generateResponse(
    query: string,
    searchResults: any[],
    context: DialogueContext,
    intent: any,
    emotion: any
  ): Promise<Omit<DialogueResponse, 'responseTime'>> {
    // 构建知识上下文
    const knowledgeContext = searchResults
      .map(result => result.content)
      .join('\n\n');

    // 构建对话历史
    const conversationHistory = context.history
      .slice(-6) // 最近3轮对话
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    // 构建系统提示词
    const systemPrompt = this.buildSystemPrompt(context, intent);

    // 构建用户提示词
    const userPrompt = this.buildUserPrompt(query, knowledgeContext, conversationHistory);

    try {
      // 调用OpenAI API
      const completion = await this.openai.chat.completions.create({
        model: this.configService.get<string>('OPENAI_MODEL', 'gpt-3.5-turbo'),
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        max_tokens: 500,
        temperature: 0.7,
        top_p: 0.9,
      });

      const responseText = completion.choices[0]?.message?.content || '抱歉，我无法理解您的问题。';

      // 分析回答的情感
      const responseEmotion = await this.emotionService.analyzeEmotion(responseText);

      return {
        text: responseText,
        intent,
        emotion: responseEmotion,
        sources: searchResults.map(result => ({
          id: result.id,
          content: result.content.substring(0, 100) + '...',
          score: result.score,
        })),
        confidence: this.calculateConfidence(searchResults, intent),
      };

    } catch (error) {
      console.error('生成回答失败:', error);
      
      // 返回默认回答
      return {
        text: '抱歉，我现在无法回答您的问题，请稍后再试。',
        intent,
        emotion: { type: 'neutral', intensity: 0.5 },
        sources: [],
        confidence: 0.1,
      };
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(context: DialogueContext, intent: any): string {
    const personality = context.personality || 'friendly';
    const responseStyle = context.responseStyle || 'professional';

    let prompt = `你是一个专业的数字人助手，具有以下特征：

性格特点：${personality}
回答风格：${responseStyle}

你的任务是基于提供的知识库内容回答用户问题。请遵循以下原则：

1. 友好、专业地回答问题
2. 基于知识库内容提供准确信息
3. 如果知识库中没有相关信息，请诚实说明
4. 保持回答简洁明了，避免冗长
5. 根据用户意图调整回答方式`;

    // 根据意图类型调整提示词
    if (intent.type === 'greeting') {
      prompt += '\n6. 用户正在打招呼，请友好地回应并询问如何帮助';
    } else if (intent.type === 'question') {
      prompt += '\n6. 用户正在询问问题，请基于知识库内容详细回答';
    } else if (intent.type === 'complaint') {
      prompt += '\n6. 用户可能在抱怨，请表示理解并提供帮助';
    }

    return prompt;
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(query: string, knowledgeContext: string, conversationHistory: string): string {
    let prompt = '';

    if (knowledgeContext) {
      prompt += `知识库内容：\n${knowledgeContext}\n\n`;
    }

    if (conversationHistory) {
      prompt += `对话历史：\n${conversationHistory}\n\n`;
    }

    prompt += `用户问题：${query}\n\n请基于上述信息回答用户问题：`;

    return prompt;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(searchResults: any[], intent: any): number {
    let confidence = 0.5; // 基础置信度

    // 基于搜索结果调整置信度
    if (searchResults.length > 0) {
      const avgScore = searchResults.reduce((sum, result) => sum + result.score, 0) / searchResults.length;
      confidence += avgScore * 0.3;
    }

    // 基于意图置信度调整
    if (intent.confidence) {
      confidence += intent.confidence * 0.2;
    }

    return Math.min(1.0, confidence);
  }

  /**
   * 保存消息
   */
  private async saveMessage(sessionId: string, messageData: any): Promise<DialogueMessage> {
    const message = this.messageRepository.create({
      sessionId,
      ...messageData,
    });

    const savedMessage = await this.messageRepository.save(message);
    return Array.isArray(savedMessage) ? savedMessage[0] : savedMessage;
  }

  /**
   * 获取会话
   */
  private async getSession(sessionId: string): Promise<DialogueSession> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId },
    });

    if (!session) {
      throw new NotFoundException('会话不存在');
    }

    if (session.status !== 'active') {
      throw new BadRequestException('会话已结束');
    }

    return session;
  }

  /**
   * 更新会话活动时间
   */
  private async updateSessionActivity(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      updatedAt: new Date(),
    });
  }

  /**
   * 获取会话历史
   */
  async getSessionHistory(sessionId: string, page: number = 1, limit: number = 20): Promise<{
    data: DialogueMessage[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.messageRepository.findAndCount({
      where: { sessionId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data: data.reverse(), total, page, limit };
  }

  /**
   * 结束会话
   */
  async endSession(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      status: 'ended',
      endedAt: new Date(),
    });

    this.eventEmitter.emit('session.ended', { sessionId });
  }
}
