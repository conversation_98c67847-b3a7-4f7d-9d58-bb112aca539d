import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class CreateSessionDto {
  @ApiProperty({ 
    description: '场景ID',
    example: 'scene_001',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  sceneId: string;

  @ApiProperty({ 
    description: '数字人ID',
    example: 'avatar_001',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  avatarId: string;

  @ApiProperty({ 
    description: '知识库ID',
    example: 'kb_001',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  knowledgeBaseId: string;

  @ApiProperty({ 
    description: '用户ID',
    example: 'user_001',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  userId?: string;

  @ApiProperty({ 
    description: '个性设置',
    example: 'friendly',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  personality?: string;

  @ApiProperty({ 
    description: '回答风格',
    example: 'professional',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  responseStyle?: string;
}
