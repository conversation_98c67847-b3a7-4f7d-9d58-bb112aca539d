import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, MaxLength } from 'class-validator';

export class SendMessageDto {
  @ApiProperty({ 
    description: '消息内容',
    example: '你好，我想了解一下产品信息',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2000)
  content: string;

  @ApiProperty({ 
    description: '消息类型',
    enum: ['text', 'audio'],
    example: 'text',
  })
  @IsEnum(['text', 'audio'])
  type: 'text' | 'audio';

  @ApiProperty({ 
    description: '音频数据（base64编码）',
    required: false,
  })
  @IsString()
  @IsOptional()
  audioData?: string;
}
