import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// 移除jieba依赖，使用简单分词

/**
 * 情感类型
 */
export enum EmotionType {
  HAPPY = 'happy',           // 开心
  SAD = 'sad',               // 悲伤
  ANGRY = 'angry',           // 愤怒
  SURPRISED = 'surprised',   // 惊讶
  FEAR = 'fear',             // 恐惧
  DISGUSTED = 'disgusted',   // 厌恶
  NEUTRAL = 'neutral',       // 中性
  EXCITED = 'excited',       // 兴奋
  CALM = 'calm',             // 平静
  CONFUSED = 'confused',     // 困惑
}

/**
 * 情感分析结果
 */
export interface EmotionResult {
  type: EmotionType;
  intensity: number;        // 强度 0-1
  confidence: number;       // 置信度 0-1
  details: {
    [key in EmotionType]?: number;
  };
}

/**
 * 情感词典条目
 */
interface EmotionWord {
  word: string;
  emotion: EmotionType;
  intensity: number;
  weight: number;
}

@Injectable()
export class EmotionService {
  private emotionDictionary: EmotionWord[] = [];
  private negationWords: Set<string> = new Set();

  constructor(private configService: ConfigService) {
    this.initializeEmotionDictionary();
    this.initializeNegationWords();
  }

  /**
   * 初始化情感词典
   */
  private initializeEmotionDictionary(): void {
    this.emotionDictionary = [
      // 开心相关
      { word: '开心', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '高兴', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '快乐', emotion: EmotionType.HAPPY, intensity: 0.9, weight: 1.0 },
      { word: '愉快', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '兴奋', emotion: EmotionType.EXCITED, intensity: 0.9, weight: 1.0 },
      { word: '激动', emotion: EmotionType.EXCITED, intensity: 0.8, weight: 1.0 },
      { word: '满意', emotion: EmotionType.HAPPY, intensity: 0.6, weight: 1.0 },
      { word: '喜欢', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '爱', emotion: EmotionType.HAPPY, intensity: 0.9, weight: 1.0 },
      { word: '棒', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '好', emotion: EmotionType.HAPPY, intensity: 0.5, weight: 0.8 },
      { word: '不错', emotion: EmotionType.HAPPY, intensity: 0.6, weight: 1.0 },

      // 悲伤相关
      { word: '悲伤', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '难过', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '伤心', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '痛苦', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '失望', emotion: EmotionType.SAD, intensity: 0.7, weight: 1.0 },
      { word: '沮丧', emotion: EmotionType.SAD, intensity: 0.7, weight: 1.0 },
      { word: '郁闷', emotion: EmotionType.SAD, intensity: 0.6, weight: 1.0 },
      { word: '哭', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },

      // 愤怒相关
      { word: '愤怒', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },
      { word: '生气', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },
      { word: '恼火', emotion: EmotionType.ANGRY, intensity: 0.7, weight: 1.0 },
      { word: '烦躁', emotion: EmotionType.ANGRY, intensity: 0.6, weight: 1.0 },
      { word: '讨厌', emotion: EmotionType.DISGUSTED, intensity: 0.7, weight: 1.0 },
      { word: '恨', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },
      { word: '气', emotion: EmotionType.ANGRY, intensity: 0.7, weight: 1.0 },
      { word: '火大', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },

      // 惊讶相关
      { word: '惊讶', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },
      { word: '震惊', emotion: EmotionType.SURPRISED, intensity: 0.9, weight: 1.0 },
      { word: '吃惊', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },
      { word: '意外', emotion: EmotionType.SURPRISED, intensity: 0.6, weight: 1.0 },
      { word: '奇怪', emotion: EmotionType.CONFUSED, intensity: 0.6, weight: 1.0 },

      // 恐惧相关
      { word: '害怕', emotion: EmotionType.FEAR, intensity: 0.8, weight: 1.0 },
      { word: '恐惧', emotion: EmotionType.FEAR, intensity: 0.9, weight: 1.0 },
      { word: '担心', emotion: EmotionType.FEAR, intensity: 0.6, weight: 1.0 },
      { word: '紧张', emotion: EmotionType.FEAR, intensity: 0.7, weight: 1.0 },
      { word: '焦虑', emotion: EmotionType.FEAR, intensity: 0.7, weight: 1.0 },

      // 厌恶相关
      { word: '厌恶', emotion: EmotionType.DISGUSTED, intensity: 0.8, weight: 1.0 },
      { word: '恶心', emotion: EmotionType.DISGUSTED, intensity: 0.9, weight: 1.0 },
      { word: '反感', emotion: EmotionType.DISGUSTED, intensity: 0.7, weight: 1.0 },

      // 平静相关
      { word: '平静', emotion: EmotionType.CALM, intensity: 0.8, weight: 1.0 },
      { word: '冷静', emotion: EmotionType.CALM, intensity: 0.8, weight: 1.0 },
      { word: '安静', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },
      { word: '放松', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },

      // 困惑相关
      { word: '困惑', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '迷惑', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '不懂', emotion: EmotionType.CONFUSED, intensity: 0.6, weight: 1.0 },
      { word: '疑惑', emotion: EmotionType.CONFUSED, intensity: 0.6, weight: 1.0 },
    ];
  }

  /**
   * 初始化否定词
   */
  private initializeNegationWords(): void {
    this.negationWords = new Set([
      '不', '没', '无', '非', '未', '别', '勿', '莫', '否',
      '不是', '没有', '不会', '不能', '不要', '不用', '不行',
      '没关系', '不对', '不好', '不错', '不满', '不够',
    ]);
  }

  /**
   * 分析情感
   */
  async analyzeEmotion(text: string): Promise<EmotionResult> {
    if (!text || text.trim().length === 0) {
      return {
        type: EmotionType.NEUTRAL,
        intensity: 0.5,
        confidence: 0.1,
        details: { [EmotionType.NEUTRAL]: 0.5 },
      };
    }

    // 预处理文本
    const processedText = this.preprocessText(text);

    // 分词
    const tokens = this.tokenize(processedText);

    // 计算情感分数
    const emotionScores = this.calculateEmotionScores(tokens, processedText);

    // 处理否定
    const adjustedScores = this.handleNegation(tokens, emotionScores);

    // 获取主要情感
    const primaryEmotion = this.getPrimaryEmotion(adjustedScores);

    // 计算置信度
    const confidence = this.calculateConfidence(adjustedScores, tokens);

    return {
      type: primaryEmotion.type,
      intensity: primaryEmotion.intensity,
      confidence,
      details: adjustedScores,
    };
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fff\w\s]/g, '') // 保留中文、英文、数字
      .trim();
  }

  /**
   * 分词
   */
  private tokenize(text: string): string[] {
    // 简单分词：按空格、标点符号分割
    return text
      .replace(/[，。！？；：、""''（）【】《》]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0);
  }

  /**
   * 计算情感分数
   */
  private calculateEmotionScores(tokens: string[], text: string): { [key in EmotionType]: number } {
    const scores: { [key in EmotionType]: number } = {
      [EmotionType.HAPPY]: 0,
      [EmotionType.SAD]: 0,
      [EmotionType.ANGRY]: 0,
      [EmotionType.SURPRISED]: 0,
      [EmotionType.FEAR]: 0,
      [EmotionType.DISGUSTED]: 0,
      [EmotionType.NEUTRAL]: 0.5, // 默认中性分数
      [EmotionType.EXCITED]: 0,
      [EmotionType.CALM]: 0,
      [EmotionType.CONFUSED]: 0,
    };

    // 匹配情感词
    for (const token of tokens) {
      const emotionWords = this.emotionDictionary.filter(item => 
        item.word === token || token.includes(item.word) || item.word.includes(token)
      );

      for (const emotionWord of emotionWords) {
        scores[emotionWord.emotion] += emotionWord.intensity * emotionWord.weight;
      }
    }

    // 检查情感符号和表情
    this.analyzeEmoticons(text, scores);

    // 检查语气词
    this.analyzeTone(text, scores);

    // 归一化分数
    return this.normalizeScores(scores);
  }

  /**
   * 分析表情符号
   */
  private analyzeEmoticons(text: string, scores: { [key in EmotionType]: number }): void {
    const emoticonPatterns = [
      { pattern: /[😊😄😃😀🙂☺️😌]/g, emotion: EmotionType.HAPPY, intensity: 0.8 },
      { pattern: /[😢😭😞😔😟]/g, emotion: EmotionType.SAD, intensity: 0.8 },
      { pattern: /[😠😡🤬😤]/g, emotion: EmotionType.ANGRY, intensity: 0.8 },
      { pattern: /[😮😯😲]/g, emotion: EmotionType.SURPRISED, intensity: 0.8 },
      { pattern: /[😨😰😱]/g, emotion: EmotionType.FEAR, intensity: 0.8 },
      { pattern: /[🤢🤮😷]/g, emotion: EmotionType.DISGUSTED, intensity: 0.8 },
      { pattern: /[😴😌🧘]/g, emotion: EmotionType.CALM, intensity: 0.8 },
      { pattern: /[😕😵‍💫🤔]/g, emotion: EmotionType.CONFUSED, intensity: 0.8 },
    ];

    for (const { pattern, emotion, intensity } of emoticonPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        scores[emotion] += matches.length * intensity;
      }
    }

    // 文本表情
    const textEmoticons = [
      { pattern: /:\)|:-\)|:\]|:D|:P/g, emotion: EmotionType.HAPPY, intensity: 0.6 },
      { pattern: /:\(|:-\(|:\[|:'/g, emotion: EmotionType.SAD, intensity: 0.6 },
      { pattern: />:\(|>:-\(/g, emotion: EmotionType.ANGRY, intensity: 0.6 },
      { pattern: /:O|:-O|:o/g, emotion: EmotionType.SURPRISED, intensity: 0.6 },
    ];

    for (const { pattern, emotion, intensity } of textEmoticons) {
      const matches = text.match(pattern);
      if (matches) {
        scores[emotion] += matches.length * intensity;
      }
    }
  }

  /**
   * 分析语气
   */
  private analyzeTone(text: string, scores: { [key in EmotionType]: number }): void {
    // 感叹号增强情感强度
    const exclamationCount = (text.match(/!/g) || []).length;
    if (exclamationCount > 0) {
      // 增强非中性情感
      Object.keys(scores).forEach(emotion => {
        if (emotion !== EmotionType.NEUTRAL && scores[emotion as EmotionType] > 0) {
          scores[emotion as EmotionType] *= (1 + exclamationCount * 0.2);
        }
      });
    }

    // 问号可能表示困惑
    const questionCount = (text.match(/\?/g) || []).length;
    if (questionCount > 0) {
      scores[EmotionType.CONFUSED] += questionCount * 0.3;
    }

    // 重复字符表示强调
    const repeatedChars = text.match(/(.)\1{2,}/g);
    if (repeatedChars) {
      Object.keys(scores).forEach(emotion => {
        if (emotion !== EmotionType.NEUTRAL && scores[emotion as EmotionType] > 0) {
          scores[emotion as EmotionType] *= 1.3;
        }
      });
    }
  }

  /**
   * 处理否定
   */
  private handleNegation(tokens: string[], scores: { [key in EmotionType]: number }): { [key in EmotionType]: number } {
    const adjustedScores = { ...scores };
    
    for (let i = 0; i < tokens.length - 1; i++) {
      const token = tokens[i];
      
      if (this.negationWords.has(token)) {
        // 检查否定词后面的情感词
        for (let j = i + 1; j < Math.min(i + 3, tokens.length); j++) {
          const nextToken = tokens[j];
          const emotionWords = this.emotionDictionary.filter(item => 
            item.word === nextToken || nextToken.includes(item.word)
          );

          for (const emotionWord of emotionWords) {
            // 反转情感
            adjustedScores[emotionWord.emotion] *= 0.3; // 减弱原情感
            
            // 增强相反情感
            const oppositeEmotion = this.getOppositeEmotion(emotionWord.emotion);
            if (oppositeEmotion) {
              adjustedScores[oppositeEmotion] += emotionWord.intensity * 0.7;
            }
          }
        }
      }
    }

    return adjustedScores;
  }

  /**
   * 获取相反情感
   */
  private getOppositeEmotion(emotion: EmotionType): EmotionType | null {
    const opposites: { [key in EmotionType]?: EmotionType } = {
      [EmotionType.HAPPY]: EmotionType.SAD,
      [EmotionType.SAD]: EmotionType.HAPPY,
      [EmotionType.ANGRY]: EmotionType.CALM,
      [EmotionType.CALM]: EmotionType.ANGRY,
      [EmotionType.EXCITED]: EmotionType.CALM,
      [EmotionType.FEAR]: EmotionType.CALM,
    };

    return opposites[emotion] || null;
  }

  /**
   * 归一化分数
   */
  private normalizeScores(scores: { [key in EmotionType]: number }): { [key in EmotionType]: number } {
    const maxScore = Math.max(...Object.values(scores));
    
    if (maxScore === 0) {
      return scores;
    }

    const normalized: { [key in EmotionType]: number } = {} as any;
    
    Object.keys(scores).forEach(emotion => {
      normalized[emotion as EmotionType] = Math.min(1.0, scores[emotion as EmotionType] / maxScore);
    });

    return normalized;
  }

  /**
   * 获取主要情感
   */
  private getPrimaryEmotion(scores: { [key in EmotionType]: number }): { type: EmotionType; intensity: number } {
    let maxEmotion = EmotionType.NEUTRAL;
    let maxScore = scores[EmotionType.NEUTRAL];

    Object.keys(scores).forEach(emotion => {
      if (scores[emotion as EmotionType] > maxScore) {
        maxEmotion = emotion as EmotionType;
        maxScore = scores[emotion as EmotionType];
      }
    });

    return {
      type: maxEmotion,
      intensity: Math.min(1.0, maxScore),
    };
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(scores: { [key in EmotionType]: number }, tokens: string[]): number {
    const maxScore = Math.max(...Object.values(scores));
    const secondMaxScore = Object.values(scores).sort((a, b) => b - a)[1] || 0;
    
    // 基于最高分和第二高分的差距计算置信度
    const scoreDifference = maxScore - secondMaxScore;
    
    // 基于文本长度调整置信度
    const lengthFactor = Math.min(1.0, tokens.length / 10);
    
    // 基于情感词数量调整置信度
    const emotionWordCount = tokens.filter(token => 
      this.emotionDictionary.some(item => item.word === token)
    ).length;
    const emotionWordFactor = Math.min(1.0, emotionWordCount / 3);
    
    return Math.min(1.0, scoreDifference * 0.5 + lengthFactor * 0.3 + emotionWordFactor * 0.2);
  }

  /**
   * 批量分析情感
   */
  async batchAnalyzeEmotion(texts: string[]): Promise<EmotionResult[]> {
    return Promise.all(texts.map(text => this.analyzeEmotion(text)));
  }

  /**
   * 获取情感统计
   */
  getEmotionStatistics(): any {
    return {
      supportedEmotions: Object.values(EmotionType),
      dictionarySize: this.emotionDictionary.length,
      negationWordsCount: this.negationWords.size,
    };
  }

  /**
   * 添加自定义情感词
   */
  addCustomEmotionWord(word: EmotionWord): void {
    this.emotionDictionary.push(word);
  }

  /**
   * 更新情感词权重
   */
  updateEmotionWordWeight(word: string, weight: number): void {
    const emotionWord = this.emotionDictionary.find(item => item.word === word);
    if (emotionWord) {
      emotionWord.weight = weight;
    }
  }
}
