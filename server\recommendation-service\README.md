# 智能推荐服务 (Recommendation Service)

## 概述

智能推荐服务是DL引擎生态系统中的核心微服务，专门负责提供多种推荐算法和个性化推荐功能。该服务基于机器学习和深度学习技术，为用户提供精准的内容推荐、协作者推荐、学习路径推荐等多种推荐场景。

## 主要功能

### 🎯 核心功能
- **多算法推荐**: 支持协同过滤、基于内容、混合推荐等多种算法
- **个性化推荐**: 基于用户画像和行为数据的个性化推荐
- **实时推荐**: 支持实时推荐请求和结果缓存
- **推荐解释**: 提供推荐结果的可解释性说明
- **反馈学习**: 基于用户反馈持续优化推荐效果
- **A/B测试**: 支持多算法对比和A/B测试

### 🚀 高级特性
- **深度学习**: 基于TensorFlow.js的神经协同过滤算法
- **缓存优化**: Redis缓存提升推荐响应速度
- **多样性控制**: 推荐结果多样性和新颖性优化
- **性能监控**: 完整的推荐效果分析和性能监控
- **微服务架构**: 支持分布式部署和水平扩展

## 技术架构

### 🏗️ 技术栈
- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL 8.0 (推荐历史和用户交互数据)
- **缓存**: Redis 7.x (推荐结果缓存)
- **机器学习**: TensorFlow.js (深度学习模型)
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker & Docker Compose

### 📊 推荐算法
1. **协同过滤 (Collaborative Filtering)**
   - 基于用户行为的相似性推荐
   - 支持用户-用户和物品-物品协同过滤

2. **基于内容推荐 (Content-Based)**
   - 基于内容特征的相似性推荐
   - 支持多维度特征匹配

3. **神经协同过滤 (Neural Collaborative Filtering)**
   - 基于深度学习的协同过滤
   - 自动学习用户和物品的潜在特征

4. **混合推荐 (Hybrid Recommendation)**
   - 结合多种算法的混合推荐
   - 动态权重调整和算法融合

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Redis 7.x
- Docker (可选)

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE recommendation_service;"

# 运行迁移
npm run migration:run
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f recommendation-service
```

## API接口

### 推荐接口

#### 获取个性化推荐
```http
POST /api/v1/recommendations
Content-Type: application/json

{
  "userId": "user_123",
  "type": "asset",
  "count": 10,
  "diversityWeight": 0.3,
  "includeExplanation": true
}
```

#### 提交用户反馈
```http
POST /api/v1/recommendations/feedback
Content-Type: application/json

{
  "userId": "user_123",
  "itemId": "item_456",
  "rating": 4.5,
  "action": "like"
}
```

### 分析接口

#### 获取推荐统计
```http
GET /api/v1/analytics/overview
```

#### 获取用户统计
```http
GET /api/v1/analytics/user/{userId}/stats
```

### 健康检查
```http
GET /api/v1/health
```

## 配置说明

### 环境变量
| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `PORT` | HTTP服务端口 | 3070 |
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 3306 |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `CACHE_TTL` | 缓存过期时间(秒) | 300 |

### 推荐算法配置
```typescript
// 算法权重配置
const algorithmWeights = {
  collaborative_filtering: 0.4,
  content_based: 0.3,
  neural_collaborative: 0.3
};

// 多样性配置
const diversityConfig = {
  diversityWeight: 0.3,
  noveltyWeight: 0.2,
  maxSimilarity: 0.8
};
```

## 开发指南

### 添加新算法
1. 在 `src/algorithms/` 目录下创建算法类
2. 实现 `RecommendationAlgorithm` 接口
3. 在 `app.module.ts` 中注册算法
4. 更新算法工厂配置

### 自定义推荐类型
1. 在 `RecommendationType` 枚举中添加新类型
2. 实现对应的推荐逻辑
3. 更新API文档和测试用例

### 性能优化
- 使用Redis缓存热门推荐结果
- 批量处理推荐请求
- 异步处理用户反馈
- 定期清理过期数据

## 监控和运维

### 健康检查
- HTTP健康检查: `GET /api/v1/health`
- 数据库连接检查
- Redis连接检查
- 模型状态检查

### 性能指标
- 推荐响应时间
- 缓存命中率
- 算法准确率
- 用户满意度

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f recommendation-service

# 查看错误日志
docker-compose logs -f recommendation-service | grep ERROR
```

## 测试

### 单元测试
```bash
npm run test
```

### 集成测试
```bash
npm run test:e2e
```

### 性能测试
```bash
npm run test:performance
```

## 部署

### 生产环境部署
1. 构建Docker镜像
2. 配置环境变量
3. 部署到Kubernetes或Docker Swarm
4. 配置负载均衡和监控

### 扩展性考虑
- 水平扩展: 支持多实例部署
- 数据分片: 大规模数据的分片存储
- 缓存集群: Redis集群部署
- 模型分布式训练: 支持分布式模型训练

## 故障排除

### 常见问题
1. **推荐结果为空**: 检查用户数据和算法配置
2. **响应时间过长**: 检查缓存配置和数据库性能
3. **模型加载失败**: 检查模型文件路径和权限
4. **Redis连接失败**: 检查Redis服务状态和网络连接

### 调试技巧
- 启用详细日志: `LOG_LEVEL=debug`
- 使用性能分析工具
- 监控系统资源使用情况

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: https://docs.dlengine.com/recommendation-service
