version: '3.8'

services:
  recommendation-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: recommendation-service
    restart: unless-stopped
    ports:
      - "3070:3070"
      - "3071:3071"
    environment:
      - NODE_ENV=production
      - PORT=3070
      - MICROSERVICE_PORT=3071
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=recommendation_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    networks:
      - recommendation-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:3070/api/v1/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  mysql:
    image: mysql:8.0
    container_name: recommendation-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYS<PERSON>_DATABASE=recommendation_service
      - MYSQL_USER=recommendation
      - MYSQL_PASSWORD=recommendation123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - recommendation-network
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: recommendation-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - recommendation-network
    command: redis-server --appendonly yes

  # 可选：Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: recommendation-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - recommendation-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  recommendation-network:
    driver: bridge
