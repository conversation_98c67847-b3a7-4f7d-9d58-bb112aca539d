Write-Host "Cleaning and installing recommendation service dependencies..." -ForegroundColor Green

# Remove node_modules directory
if (Test-Path "node_modules") {
    Write-Host "Removing node_modules directory..." -ForegroundColor Yellow
    Remove-Item "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
}

# Remove package-lock.json
if (Test-Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Yellow
    Remove-Item "package-lock.json" -Force -ErrorAction SilentlyContinue
}

# Clean npm cache
Write-Host "Cleaning npm cache..." -ForegroundColor Yellow
npm cache clean --force

# Configure npm
Write-Host "Configuring npm settings..." -ForegroundColor Yellow
npm config set fund false
npm config set audit false

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Green
npm install --no-optional --no-fund --no-audit --legacy-peer-deps

if ($LASTEXITCODE -eq 0) {
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
    
    if (Test-Path "node_modules") {
        Write-Host "node_modules directory created successfully" -ForegroundColor Green
    }
    
    if (Test-Path "scripts/verify-project.js") {
        Write-Host "Running project verification..." -ForegroundColor Cyan
        node scripts/verify-project.js
    }
} else {
    Write-Host "Dependency installation failed" -ForegroundColor Red
    Write-Host "Suggested solutions:" -ForegroundColor Yellow
    Write-Host "1. Run PowerShell as Administrator" -ForegroundColor White
    Write-Host "2. Check network connection" -ForegroundColor White
    Write-Host "3. Update Node.js to LTS version" -ForegroundColor White
    Write-Host "4. Try using yarn instead: yarn install" -ForegroundColor White
}
