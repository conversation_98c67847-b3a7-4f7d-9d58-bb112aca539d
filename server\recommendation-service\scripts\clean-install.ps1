# PowerShell脚本：清理并重新安装依赖
# 解决Windows文件权限和依赖安装问题

Write-Host "🧹 开始清理推荐服务依赖..." -ForegroundColor Green

# 检查是否以管理员权限运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  建议以管理员权限运行此脚本以避免权限问题" -ForegroundColor Yellow
}

# 设置错误处理
$ErrorActionPreference = "Continue"

# 获取当前目录
$projectRoot = Split-Path -Parent $PSScriptRoot

Write-Host "📁 项目目录: $projectRoot" -ForegroundColor Cyan

# 停止可能占用文件的进程
Write-Host "🛑 停止可能的Node.js进程..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# 等待进程完全停止
Start-Sleep -Seconds 2

# 强制删除node_modules目录
$nodeModulesPath = Join-Path $projectRoot "node_modules"
if (Test-Path $nodeModulesPath) {
    Write-Host "🗑️  删除node_modules目录..." -ForegroundColor Yellow
    
    try {
        # 尝试正常删除
        Remove-Item $nodeModulesPath -Recurse -Force -ErrorAction Stop
        Write-Host "✅ node_modules删除成功" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  正常删除失败，尝试强制删除..." -ForegroundColor Yellow
        
        # 使用robocopy清空目录（Windows推荐方法）
        $emptyDir = Join-Path $env:TEMP "empty_dir_for_robocopy"
        if (-not (Test-Path $emptyDir)) {
            New-Item -ItemType Directory -Path $emptyDir -Force | Out-Null
        }
        
        # 使用robocopy镜像空目录到node_modules（相当于清空）
        robocopy $emptyDir $nodeModulesPath /MIR /NFL /NDL /NJH /NJS /NC /NS /NP
        
        # 删除空目录
        Remove-Item $emptyDir -Force -ErrorAction SilentlyContinue
        
        # 最后删除node_modules目录本身
        Remove-Item $nodeModulesPath -Force -ErrorAction SilentlyContinue
        
        if (-not (Test-Path $nodeModulesPath)) {
            Write-Host "✅ node_modules强制删除成功" -ForegroundColor Green
        }
    }
}

# 删除package-lock.json
$packageLockPath = Join-Path $projectRoot "package-lock.json"
if (Test-Path $packageLockPath) {
    Write-Host "🗑️  删除package-lock.json..." -ForegroundColor Yellow
    Remove-Item $packageLockPath -Force
    Write-Host "✅ package-lock.json删除成功" -ForegroundColor Green
}

# 清理npm缓存
Write-Host "🧹 清理npm缓存..." -ForegroundColor Yellow
npm cache clean --force

# 设置npm配置以避免权限问题
Write-Host "⚙️  配置npm设置..." -ForegroundColor Yellow
npm config set fund false
npm config set audit false

# 重新安装依赖
Write-Host "📦 重新安装依赖..." -ForegroundColor Green
Set-Location $projectRoot

# 使用更安全的安装选项
npm install --no-optional --no-fund --no-audit --legacy-peer-deps

if ($LASTEXITCODE -eq 0) {
    Write-Host "🎉 依赖安装成功！" -ForegroundColor Green
    
    # 验证安装
    Write-Host "🔍 验证安装结果..." -ForegroundColor Cyan
    if (Test-Path "node_modules") {
        $moduleCount = (Get-ChildItem "node_modules" -Directory).Count
        Write-Host "✅ 已安装 $moduleCount 个模块" -ForegroundColor Green
    }
    
    # 运行项目验证脚本
    if (Test-Path "scripts\verify-project.js") {
        Write-Host "🔍 运行项目验证..." -ForegroundColor Cyan
        node scripts\verify-project.js
    }
    
} else {
    Write-Host "❌ 依赖安装失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
    Write-Host "💡 建议尝试以下解决方案：" -ForegroundColor Yellow
    Write-Host "   1. 以管理员权限运行PowerShell" -ForegroundColor White
    Write-Host "   2. 检查网络连接" -ForegroundColor White
    Write-Host "   3. 更新Node.js到LTS版本" -ForegroundColor White
    Write-Host "   4. 使用yarn代替npm: yarn install" -ForegroundColor White
}

Write-Host "🏁 清理安装脚本执行完成" -ForegroundColor Green
