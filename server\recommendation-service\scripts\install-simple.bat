@echo off
echo 🧹 清理并安装推荐服务依赖...

REM 删除node_modules目录
if exist node_modules (
    echo 🗑️  删除node_modules目录...
    rmdir /s /q node_modules
)

REM 删除package-lock.json
if exist package-lock.json (
    echo 🗑️  删除package-lock.json...
    del package-lock.json
)

REM 清理npm缓存
echo 🧹 清理npm缓存...
npm cache clean --force

REM 设置npm配置
echo ⚙️  配置npm设置...
npm config set fund false
npm config set audit false

REM 安装依赖
echo 📦 安装依赖...
npm install --no-optional --no-fund --no-audit --legacy-peer-deps

if %errorlevel% equ 0 (
    echo 🎉 依赖安装成功！
    echo 🔍 验证安装结果...
    if exist node_modules (
        echo ✅ node_modules目录已创建
    )
    
    REM 运行项目验证
    if exist scripts\verify-project.js (
        echo 🔍 运行项目验证...
        node scripts\verify-project.js
    )
) else (
    echo ❌ 依赖安装失败
    echo 💡 建议尝试以下解决方案：
    echo    1. 以管理员权限运行命令提示符
    echo    2. 检查网络连接
    echo    3. 更新Node.js到LTS版本
    echo    4. 使用yarn代替npm: yarn install
)

pause
