/**
 * 神经协同过滤推荐算法
 * 基于矩阵分解的协同过滤实现（简化版）
 */
import { Injectable, Logger } from '@nestjs/common';
import { Matrix } from 'ml-matrix';
import {
  RecommendationAlgorithm,
  RecommendationRequest,
  RecommendationResponse,
  UserProfile,
  TrainingData,
  ModelConfig
} from '../interfaces/recommendation.interface';

@Injectable()
export class NeuralCollaborativeFiltering implements RecommendationAlgorithm {
  private readonly logger = new Logger(NeuralCollaborativeFiltering.name);
  private userFactors: Matrix | null = null;
  private itemFactors: Matrix | null = null;
  private userEmbeddings: Map<string, number[]> = new Map();
  private itemEmbeddings: Map<string, number[]> = new Map();
  private userToIndex: Map<string, number> = new Map();
  private itemToIndex: Map<string, number> = new Map();
  private indexToUser: Map<number, string> = new Map();
  private indexToItem: Map<number, string> = new Map();
  private userMeans: Map<string, number> = new Map();
  private globalMean: number = 0;

  constructor(private config: ModelConfig = {
    embeddingDim: 64,
    hiddenLayers: [128, 64, 32],
    dropoutRate: 0.2,
    learningRate: 0.001
  }) {}

  /**
   * 生成推荐
   */
  async recommend(request: RecommendationRequest): Promise<RecommendationResponse[]> {
    if (!this.userFactors || !this.itemFactors) {
      await this.loadModel();
    }

    const userId = request.userId;
    const userIndex = this.userToIndex.get(userId);

    if (userIndex === undefined) {
      // 新用户，使用冷启动策略
      return this.handleColdStart(request);
    }

    // 获取候选物品
    const candidateItems = await this.getCandidateItems(request);

    // 批量预测评分
    const predictions = await this.batchPredict(userIndex, candidateItems);

    // 构建推荐结果
    const recommendations = predictions
      .map((score, index) => ({
        itemId: candidateItems[index].id,
        itemType: candidateItems[index].type,
        score,
        algorithm: 'matrix_factorization',
        confidence: this.calculateConfidence(score),
        explanation: this.generateExplanation(userId, candidateItems[index], score),
        context: request.context
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, request.count || 10);

    return recommendations;
  }

  /**
   * 批量预测评分
   */
  private async batchPredict(userIndex: number, items: any[]): Promise<number[]> {
    if (!this.userFactors || !this.itemFactors) {
      throw new Error('模型未加载');
    }

    const itemIndices = items.map(item => this.itemToIndex.get(item.id) || 0);
    const predictions: number[] = [];

    try {
      // 获取用户因子向量
      const userVector = this.userFactors.getRow(userIndex);

      for (const itemIndex of itemIndices) {
        if (itemIndex < this.itemFactors.rows) {
          // 获取物品因子向量
          const itemVector = this.itemFactors.getRow(itemIndex);

          // 计算点积作为预测评分
          let score = 0;
          for (let i = 0; i < userVector.length; i++) {
            score += userVector[i] * itemVector[i];
          }

          // 添加偏置项
          const userId = this.indexToUser.get(userIndex);
          const userMean = userId ? this.userMeans.get(userId) || this.globalMean : this.globalMean;
          score += userMean;

          // 归一化到0-1范围
          score = Math.max(0, Math.min(1, (score + 1) / 2));
          predictions.push(score);
        } else {
          predictions.push(0.5); // 默认分数
        }
      }

      return predictions;
    } catch (error) {
      this.logger.error('批量预测失败:', error);
      return items.map(() => 0.5); // 返回默认分数
    }
  }

  /**
   * 训练模型
   */
  async train(trainingData: TrainingData): Promise<void> {
    this.logger.log('开始训练矩阵分解模型...');

    // 准备训练数据
    const { userMatrix, itemMatrix, ratingMatrix } = this.prepareTrainingData(trainingData);

    // 使用SVD进行矩阵分解
    const factors = this.config.embeddingDim || 50;
    const learningRate = this.config.learningRate || 0.01;
    const regularization = 0.01;
    const epochs = this.config.epochs || 100;

    // 初始化用户和物品因子矩阵
    this.userFactors = Matrix.random(userMatrix.rows, factors, { random: () => Math.random() * 0.1 });
    this.itemFactors = Matrix.random(itemMatrix.rows, factors, { random: () => Math.random() * 0.1 });

    // 随机梯度下降训练
    for (let epoch = 0; epoch < epochs; epoch++) {
      let totalError = 0;
      let count = 0;

      // 遍历所有评分数据
      for (const interaction of trainingData.interactions) {
        const userIndex = this.userToIndex.get(interaction.userId);
        const itemIndex = this.itemToIndex.get(interaction.itemId);

        if (userIndex !== undefined && itemIndex !== undefined) {
          // 计算预测评分
          const userVector = this.userFactors.getRow(userIndex);
          const itemVector = this.itemFactors.getRow(itemIndex);

          let prediction = 0;
          for (let f = 0; f < factors; f++) {
            prediction += userVector[f] * itemVector[f];
          }

          // 计算误差
          const error = interaction.rating - prediction;
          totalError += error * error;
          count++;

          // 更新因子
          for (let f = 0; f < factors; f++) {
            const userFeature = userVector[f];
            const itemFeature = itemVector[f];

            // 梯度更新
            this.userFactors.set(userIndex, f,
              userFeature + learningRate * (error * itemFeature - regularization * userFeature)
            );
            this.itemFactors.set(itemIndex, f,
              itemFeature + learningRate * (error * userFeature - regularization * itemFeature)
            );
          }
        }
      }

      if (epoch % 10 === 0) {
        const rmse = Math.sqrt(totalError / count);
        this.logger.log(`Epoch ${epoch}: RMSE = ${rmse.toFixed(4)}`);
      }
    }

    // 更新嵌入向量
    await this.updateEmbeddings();

    this.logger.log('模型训练完成');
  }

  /**
   * 构建用户-物品矩阵
   */
  private buildUserItemMatrix(interactions: any[]): { userMatrix: Matrix, itemMatrix: Matrix, ratingMatrix: Matrix } {
    const numUsers = this.userToIndex.size;
    const numItems = this.itemToIndex.size;

    // 创建评分矩阵
    const ratingData = Array(numUsers).fill(null).map(() => Array(numItems).fill(0));

    for (const interaction of interactions) {
      const userIndex = this.userToIndex.get(interaction.userId);
      const itemIndex = this.itemToIndex.get(interaction.itemId);

      if (userIndex !== undefined && itemIndex !== undefined) {
        ratingData[userIndex][itemIndex] = interaction.rating;
      }
    }

    const ratingMatrix = new Matrix(ratingData);

    // 创建用户和物品标识矩阵
    const userMatrix = Matrix.eye(numUsers);
    const itemMatrix = Matrix.eye(numItems);

    return { userMatrix, itemMatrix, ratingMatrix };
  }

  /**
   * 准备训练数据
   */
  private prepareTrainingData(trainingData: TrainingData): {
    userMatrix: Matrix;
    itemMatrix: Matrix;
    ratingMatrix: Matrix;
  } {
    const interactions = trainingData.interactions;

    // 构建用户和物品索引映射
    this.buildIndexMappings(interactions);

    // 计算全局平均评分和用户平均评分
    this.calculateMeans(interactions);

    // 构建矩阵
    return this.buildUserItemMatrix(interactions);
  }

  /**
   * 构建索引映射
   */
  private buildIndexMappings(interactions: any[]): void {
    const users = new Set<string>();
    const items = new Set<string>();

    for (const interaction of interactions) {
      users.add(interaction.userId);
      items.add(interaction.itemId);
    }

    // 构建用户索引映射
    let userIndex = 0;
    for (const userId of users) {
      this.userToIndex.set(userId, userIndex);
      this.indexToUser.set(userIndex, userId);
      userIndex++;
    }

    // 构建物品索引映射
    let itemIndex = 0;
    for (const itemId of items) {
      this.itemToIndex.set(itemId, itemIndex);
      this.indexToItem.set(itemIndex, itemId);
      itemIndex++;
    }
  }

  /**
   * 计算平均评分
   */
  private calculateMeans(interactions: any[]): void {
    // 计算全局平均评分
    const totalRating = interactions.reduce((sum, interaction) => sum + interaction.rating, 0);
    this.globalMean = totalRating / interactions.length;

    // 计算每个用户的平均评分
    const userRatings: Map<string, number[]> = new Map();

    for (const interaction of interactions) {
      if (!userRatings.has(interaction.userId)) {
        userRatings.set(interaction.userId, []);
      }
      userRatings.get(interaction.userId)!.push(interaction.rating);
    }

    for (const [userId, ratings] of userRatings) {
      const userMean = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      this.userMeans.set(userId, userMean);
    }
  }

  /**
   * 更新嵌入向量
   */
  private async updateEmbeddings(): Promise<void> {
    if (!this.userFactors || !this.itemFactors) return;

    // 更新用户嵌入映射
    for (const [userId, index] of this.userToIndex) {
      if (index < this.userFactors.rows) {
        const embedding = this.userFactors.getRow(index);
        this.userEmbeddings.set(userId, embedding);
      }
    }

    // 更新物品嵌入映射
    for (const [itemId, index] of this.itemToIndex) {
      if (index < this.itemFactors.rows) {
        const embedding = this.itemFactors.getRow(index);
        this.itemEmbeddings.set(itemId, embedding);
      }
    }
  }

  /**
   * 处理冷启动问题
   */
  private async handleColdStart(request: RecommendationRequest): Promise<RecommendationResponse[]> {
    // 对于新用户，基于流行度和内容特征推荐
    const popularItems = await this.getPopularItems(request.type);
    
    return popularItems.slice(0, request.count || 10).map((item, index) => ({
      itemId: item.id,
      itemType: item.type,
      score: 0.8 - (index * 0.05), // 递减分数
      algorithm: 'cold_start_popularity',
      confidence: 0.6,
      explanation: '基于热门内容的推荐',
      context: request.context
    }));
  }

  /**
   * 获取候选物品
   */
  private async getCandidateItems(request: RecommendationRequest): Promise<any[]> {
    // 这里应该从物品服务获取候选物品
    // 可以基于类型、过滤条件等筛选
    return [];
  }

  /**
   * 获取热门物品
   */
  private async getPopularItems(type: string): Promise<any[]> {
    // 这里应该从统计服务获取热门物品
    return [];
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(score: number): number {
    // 基于分数计算置信度
    return Math.min(0.95, Math.max(0.1, score));
  }

  /**
   * 生成解释
   */
  private generateExplanation(userId: string, item: any, score: number): string {
    return `基于您的历史行为和偏好，我们认为您会喜欢这个${item.type}（置信度：${(score * 100).toFixed(0)}%）`;
  }

  /**
   * 加载模型
   */
  private async loadModel(): Promise<void> {
    try {
      // 这里可以从文件系统加载预训练的矩阵分解模型
      // 暂时使用随机初始化
      const factors = this.config.embeddingDim || 50;
      const numUsers = Math.max(this.userToIndex.size, 100);
      const numItems = Math.max(this.itemToIndex.size, 100);

      this.userFactors = Matrix.random(numUsers, factors, { random: () => Math.random() * 0.1 });
      this.itemFactors = Matrix.random(numItems, factors, { random: () => Math.random() * 0.1 });

      this.logger.log('模型初始化完成');
    } catch (error) {
      this.logger.warn('模型加载失败，使用默认初始化');
      const factors = this.config.embeddingDim || 50;
      this.userFactors = Matrix.random(100, factors, { random: () => Math.random() * 0.1 });
      this.itemFactors = Matrix.random(100, factors, { random: () => Math.random() * 0.1 });
    }
  }

  /**
   * 保存模型
   */
  async saveModel(path: string): Promise<void> {
    if (!this.userFactors || !this.itemFactors) {
      throw new Error('没有可保存的模型');
    }

    // 这里可以实现模型保存逻辑
    // 例如保存为JSON格式或二进制格式
    const modelData = {
      userFactors: this.userFactors.to2DArray(),
      itemFactors: this.itemFactors.to2DArray(),
      userToIndex: Object.fromEntries(this.userToIndex),
      itemToIndex: Object.fromEntries(this.itemToIndex),
      userMeans: Object.fromEntries(this.userMeans),
      globalMean: this.globalMean
    };

    // 这里应该实际保存到文件系统
    this.logger.log(`模型数据准备完成，可保存到: ${path}`);
  }
}
