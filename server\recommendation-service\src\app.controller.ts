/**
 * 应用程序主控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('app')
@Controller()
export class AppController {
  
  @Get()
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ 
    status: 200, 
    description: '服务信息',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: '智能推荐服务' },
        version: { type: 'string', example: '1.0.0' },
        description: { type: 'string', example: '提供多种推荐算法和个性化推荐功能' },
        status: { type: 'string', example: 'running' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getServiceInfo() {
    return {
      name: '智能推荐服务',
      version: '1.0.0',
      description: '提供多种推荐算法和个性化推荐功能的微服务',
      status: 'running',
      timestamp: new Date().toISOString(),
      features: [
        '协同过滤推荐',
        '基于内容的推荐',
        '混合推荐算法',
        '深度学习推荐',
        '实时推荐',
        '个性化推荐',
        '推荐解释',
        'A/B测试支持'
      ],
      endpoints: {
        recommendations: '/api/v1/recommendations',
        feedback: '/api/v1/feedback',
        analytics: '/api/v1/analytics',
        health: '/api/v1/health',
        docs: '/docs'
      }
    };
  }

  @Get('ping')
  @ApiOperation({ summary: '服务健康检查' })
  @ApiResponse({ 
    status: 200, 
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'pong' },
        timestamp: { type: 'string', format: 'date-time' },
        uptime: { type: 'number', example: 12345 }
      }
    }
  })
  ping() {
    return {
      message: 'pong',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  }
}
