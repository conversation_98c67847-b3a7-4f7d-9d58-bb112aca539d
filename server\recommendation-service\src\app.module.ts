/**
 * 智能推荐服务主模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TerminusModule } from '@nestjs/terminus';

// 实体
import { RecommendationHistory } from './entities/recommendation-history.entity';
import { UserInteraction } from './entities/user-interaction.entity';
import { UserProfileEntity } from './entities/user-profile.entity';

// 服务
import { RecommendationService } from './recommendation.service';
import { CacheManager } from './cache/cache.manager';
import { ModelManager } from './models/model.manager';

// 控制器
import { AppController } from './app.controller';
import { RecommendationController } from './controllers/recommendation.controller';
import { AnalyticsController } from './controllers/analytics.controller';
import { HealthController } from './controllers/health.controller';

// 算法
import { NeuralCollaborativeFiltering } from './algorithms/neural-collaborative-filtering';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', ''),
        database: configService.get<string>('DB_DATABASE', 'recommendation_service'),
        entities: [RecommendationHistory, UserInteraction, UserProfileEntity],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
        logging: configService.get<boolean>('DB_LOGGING', false),
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // TypeORM实体模块
    TypeOrmModule.forFeature([
      RecommendationHistory,
      UserInteraction,
      UserProfileEntity,
    ]),

    // 调度模块
    ScheduleModule.forRoot(),

    // 事件模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 健康检查模块
    TerminusModule,
  ],

  controllers: [
    AppController,
    RecommendationController,
    AnalyticsController,
    HealthController,
  ],

  providers: [
    // 核心服务
    RecommendationService,
    CacheManager,
    ModelManager,

    // 算法提供者
    NeuralCollaborativeFiltering,

    // 算法工厂
    {
      provide: 'ALGORITHM_FACTORY',
      useFactory: (ncf: NeuralCollaborativeFiltering) => {
        return {
          neural_collaborative_filtering: ncf,
          // 可以在这里添加更多算法
        };
      },
      inject: [NeuralCollaborativeFiltering],
    },
  ],

  exports: [
    RecommendationService,
    CacheManager,
    ModelManager,
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 启动时日志
    console.log('🚀 智能推荐服务模块已加载');
    console.log(`📊 数据库: ${this.configService.get<string>('DB_HOST')}:${this.configService.get<number>('DB_PORT')}`);
    console.log(`🗄️  Redis: ${this.configService.get<string>('REDIS_HOST')}:${this.configService.get<number>('REDIS_PORT')}`);
    console.log(`🔧 环境: ${this.configService.get<string>('NODE_ENV', 'development')}`);
  }
}
