/**
 * 分析统计控制器
 */
import { 
  Controller, 
  Get, 
  Query, 
  Param,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiParam
} from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { RecommendationHistory } from '../entities/recommendation-history.entity';
import { UserInteraction } from '../entities/user-interaction.entity';
import { CacheManager } from '../cache/cache.manager';

@ApiTags('analytics')
@Controller('analytics')
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(
    @InjectRepository(RecommendationHistory)
    private historyRepository: Repository<RecommendationHistory>,
    @InjectRepository(UserInteraction)
    private interactionRepository: Repository<UserInteraction>,
    private cacheManager: CacheManager
  ) {}

  @Get('overview')
  @ApiOperation({ summary: '获取推荐系统概览统计' })
  @ApiResponse({ 
    status: 200, 
    description: '概览统计数据',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            totalRecommendations: { type: 'number', example: 10000 },
            totalUsers: { type: 'number', example: 1500 },
            totalInteractions: { type: 'number', example: 25000 },
            averageClickRate: { type: 'number', example: 0.15 },
            averageRating: { type: 'number', example: 4.2 },
            topAlgorithms: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  algorithm: { type: 'string' },
                  count: { type: 'number' },
                  performance: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  })
  async getOverview() {
    try {
      const cacheKey = 'analytics:overview';
      const cached = await this.cacheManager.get(cacheKey);
      
      if (cached) {
        return { success: true, data: cached };
      }

      // 总推荐数
      const totalRecommendations = await this.historyRepository.count();
      
      // 总用户数
      const totalUsers = await this.historyRepository
        .createQueryBuilder('history')
        .select('COUNT(DISTINCT history.userId)', 'count')
        .getRawOne();
      
      // 总交互数
      const totalInteractions = await this.interactionRepository.count();
      
      // 点击率
      const clickedRecommendations = await this.historyRepository.count({
        where: { clicked: true }
      });
      const averageClickRate = totalRecommendations > 0 ? 
        clickedRecommendations / totalRecommendations : 0;
      
      // 平均评分
      const avgRatingResult = await this.interactionRepository
        .createQueryBuilder('interaction')
        .select('AVG(interaction.rating)', 'avgRating')
        .where('interaction.rating IS NOT NULL')
        .getRawOne();
      
      // 算法性能统计
      const algorithmStats = await this.historyRepository
        .createQueryBuilder('history')
        .select([
          'history.algorithm',
          'COUNT(*) as count',
          'AVG(history.score) as avgScore',
          'SUM(CASE WHEN history.clicked = true THEN 1 ELSE 0 END) as clicks'
        ])
        .groupBy('history.algorithm')
        .orderBy('count', 'DESC')
        .limit(5)
        .getRawMany();

      const topAlgorithms = algorithmStats.map(stat => ({
        algorithm: stat.history_algorithm,
        count: parseInt(stat.count),
        avgScore: parseFloat(stat.avgScore || 0),
        clickRate: stat.count > 0 ? parseInt(stat.clicks) / parseInt(stat.count) : 0
      }));

      const overview = {
        totalRecommendations,
        totalUsers: parseInt(totalUsers.count),
        totalInteractions,
        averageClickRate: Math.round(averageClickRate * 10000) / 100, // 百分比
        averageRating: parseFloat(avgRatingResult?.avgRating || 0),
        topAlgorithms
      };

      // 缓存5分钟
      await this.cacheManager.set(cacheKey, overview, 300);
      
      return { success: true, data: overview };
      
    } catch (error) {
      this.logger.error(`获取概览统计失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取统计数据失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('user/:userId/stats')
  @ApiOperation({ summary: '获取用户推荐统计' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({ 
    status: 200, 
    description: '用户统计数据',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            totalRecommendations: { type: 'number' },
            totalInteractions: { type: 'number' },
            clickRate: { type: 'number' },
            averageRating: { type: 'number' },
            preferredCategories: { type: 'array', items: { type: 'string' } },
            activityTrend: { type: 'array', items: { type: 'object' } }
          }
        }
      }
    }
  })
  async getUserStats(@Param('userId') userId: string) {
    try {
      const cacheKey = `analytics:user:${userId}`;
      const cached = await this.cacheManager.get(cacheKey);
      
      if (cached) {
        return { success: true, data: cached };
      }

      // 用户推荐统计
      const totalRecommendations = await this.historyRepository.count({
        where: { userId }
      });
      
      const clickedRecommendations = await this.historyRepository.count({
        where: { userId, clicked: true }
      });
      
      const clickRate = totalRecommendations > 0 ? 
        clickedRecommendations / totalRecommendations : 0;

      // 用户交互统计
      const totalInteractions = await this.interactionRepository.count({
        where: { userId }
      });
      
      const avgRatingResult = await this.interactionRepository
        .createQueryBuilder('interaction')
        .select('AVG(interaction.rating)', 'avgRating')
        .where('interaction.userId = :userId', { userId })
        .andWhere('interaction.rating IS NOT NULL')
        .getRawOne();

      // 偏好类别统计
      const categoryStats = await this.historyRepository
        .createQueryBuilder('history')
        .select([
          'history.itemType',
          'COUNT(*) as count'
        ])
        .where('history.userId = :userId', { userId })
        .groupBy('history.itemType')
        .orderBy('count', 'DESC')
        .limit(5)
        .getRawMany();

      const preferredCategories = categoryStats.map(stat => stat.history_itemType);

      // 活动趋势（最近7天）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const activityTrend = await this.interactionRepository
        .createQueryBuilder('interaction')
        .select([
          'DATE(interaction.timestamp) as date',
          'COUNT(*) as count'
        ])
        .where('interaction.userId = :userId', { userId })
        .andWhere('interaction.timestamp >= :startDate', { startDate: sevenDaysAgo })
        .groupBy('DATE(interaction.timestamp)')
        .orderBy('date', 'ASC')
        .getRawMany();

      const userStats = {
        userId,
        totalRecommendations,
        totalInteractions,
        clickRate: Math.round(clickRate * 10000) / 100,
        averageRating: parseFloat(avgRatingResult?.avgRating || 0),
        preferredCategories,
        activityTrend: activityTrend.map(trend => ({
          date: trend.date,
          count: parseInt(trend.count)
        }))
      };

      // 缓存2分钟
      await this.cacheManager.set(cacheKey, userStats, 120);
      
      return { success: true, data: userStats };
      
    } catch (error) {
      this.logger.error(`获取用户统计失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取用户统计失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('performance')
  @ApiOperation({ summary: '获取推荐性能指标' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'algorithm', required: false, description: '算法名称' })
  @ApiResponse({ 
    status: 200, 
    description: '性能指标数据'
  })
  async getPerformanceMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('algorithm') algorithm?: string
  ) {
    try {
      const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const end = endDate ? new Date(endDate) : new Date();
      
      let query = this.historyRepository
        .createQueryBuilder('history')
        .where('history.timestamp BETWEEN :start AND :end', { start, end });
      
      if (algorithm) {
        query = query.andWhere('history.algorithm = :algorithm', { algorithm });
      }

      const recommendations = await query.getMany();
      
      // 计算性能指标
      const totalRecommendations = recommendations.length;
      const clickedRecommendations = recommendations.filter(r => r.clicked).length;
      const convertedRecommendations = recommendations.filter(r => r.converted).length;
      
      const clickThroughRate = totalRecommendations > 0 ? 
        clickedRecommendations / totalRecommendations : 0;
      
      const conversionRate = totalRecommendations > 0 ? 
        convertedRecommendations / totalRecommendations : 0;
      
      const averageScore = recommendations.length > 0 ?
        recommendations.reduce((sum, r) => sum + r.score, 0) / recommendations.length : 0;
      
      const averageConfidence = recommendations.length > 0 ?
        recommendations.reduce((sum, r) => sum + (r.confidence || 0), 0) / recommendations.length : 0;

      // 按算法分组的性能
      const algorithmPerformance = recommendations.reduce((acc, rec) => {
        if (!acc[rec.algorithm]) {
          acc[rec.algorithm] = {
            algorithm: rec.algorithm,
            count: 0,
            clicks: 0,
            conversions: 0,
            totalScore: 0
          };
        }
        
        acc[rec.algorithm].count++;
        if (rec.clicked) acc[rec.algorithm].clicks++;
        if (rec.converted) acc[rec.algorithm].conversions++;
        acc[rec.algorithm].totalScore += rec.score;
        
        return acc;
      }, {} as Record<string, any>);

      const algorithmStats = Object.values(algorithmPerformance).map((perf: any) => ({
        algorithm: perf.algorithm,
        count: perf.count,
        clickRate: perf.count > 0 ? perf.clicks / perf.count : 0,
        conversionRate: perf.count > 0 ? perf.conversions / perf.count : 0,
        averageScore: perf.count > 0 ? perf.totalScore / perf.count : 0
      }));

      return {
        success: true,
        data: {
          period: { startDate: start, endDate: end },
          overall: {
            totalRecommendations,
            clickThroughRate: Math.round(clickThroughRate * 10000) / 100,
            conversionRate: Math.round(conversionRate * 10000) / 100,
            averageScore: Math.round(averageScore * 10000) / 10000,
            averageConfidence: Math.round(averageConfidence * 10000) / 10000
          },
          byAlgorithm: algorithmStats
        }
      };
      
    } catch (error) {
      this.logger.error(`获取性能指标失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取性能指标失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('cache/stats')
  @ApiOperation({ summary: '获取缓存统计信息' })
  @ApiResponse({ 
    status: 200, 
    description: '缓存统计数据'
  })
  async getCacheStats() {
    try {
      const stats = await this.cacheManager.getStats();
      
      return {
        success: true,
        data: stats
      };
      
    } catch (error) {
      this.logger.error(`获取缓存统计失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取缓存统计失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
