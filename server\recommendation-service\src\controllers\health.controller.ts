/**
 * 健康检查控制器
 */
import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { 
  HealthCheckService, 
  HealthCheck, 
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator
} from '@nestjs/terminus';
import { CacheManager } from '../cache/cache.manager';
import { ModelManager } from '../models/model.manager';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private cacheManager: CacheManager,
    private modelManager: ModelManager
  ) {}

  @Get()
  @ApiOperation({ summary: '综合健康检查' })
  @ApiResponse({ 
    status: 200, 
    description: '健康检查结果',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024), // 150MB
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024),   // 300MB
      
      // 磁盘空间检查
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 // 90%阈值
      }),
      
      // Redis缓存检查
      () => this.checkRedis(),
      
      // 模型状态检查
      () => this.checkModels(),
      
      // 服务状态检查
      () => this.checkServiceStatus()
    ]);
  }

  @Get('database')
  @ApiOperation({ summary: '数据库健康检查' })
  @ApiResponse({ status: 200, description: '数据库状态' })
  @HealthCheck()
  checkDatabase() {
    return this.health.check([
      () => this.db.pingCheck('database')
    ]);
  }

  @Get('memory')
  @ApiOperation({ summary: '内存使用检查' })
  @ApiResponse({ status: 200, description: '内存状态' })
  @HealthCheck()
  checkMemory() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024)
    ]);
  }

  @Get('cache')
  @ApiOperation({ summary: '缓存健康检查' })
  @ApiResponse({ status: 200, description: '缓存状态' })
  @HealthCheck()
  checkCache() {
    return this.health.check([
      () => this.checkRedis()
    ]);
  }

  @Get('models')
  @ApiOperation({ summary: '模型状态检查' })
  @ApiResponse({ status: 200, description: '模型状态' })
  @HealthCheck()
  checkModelsHealth() {
    return this.health.check([
      () => this.checkModels()
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康状态' })
  @ApiResponse({ 
    status: 200, 
    description: '详细健康状态信息',
    schema: {
      type: 'object',
      properties: {
        service: { type: 'object' },
        system: { type: 'object' },
        dependencies: { type: 'object' },
        performance: { type: 'object' }
      }
    }
  })
  async getDetailedHealth() {
    try {
      const startTime = Date.now();
      
      // 服务信息
      const serviceInfo = {
        name: '智能推荐服务',
        version: '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        pid: process.pid
      };

      // 系统资源
      const memoryUsage = process.memoryUsage();
      const systemInfo = {
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024)
        },
        cpu: process.cpuUsage(),
        platform: process.platform,
        arch: process.arch
      };

      // 依赖服务状态
      const dependencies = {
        database: await this.checkDatabaseConnection(),
        redis: await this.checkRedisConnection(),
        models: await this.checkModelStatus()
      };

      // 性能指标
      const responseTime = Date.now() - startTime;
      const performance = {
        responseTime,
        averageResponseTime: responseTime, // 简化实现
        requestsPerSecond: 0, // 需要实际统计
        errorRate: 0 // 需要实际统计
      };

      return {
        service: serviceInfo,
        system: systemInfo,
        dependencies,
        performance,
        timestamp: new Date().toISOString(),
        status: this.calculateOverallStatus(dependencies)
      };

    } catch (error) {
      this.logger.error(`详细健康检查失败: ${error.message}`, error.stack);
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Redis连接检查
   */
  private async checkRedis(): Promise<any> {
    try {
      await this.cacheManager.get('health_check');
      return {
        'redis': {
          status: 'up',
          message: 'Redis连接正常'
        }
      };
    } catch (error) {
      throw new Error(`Redis连接失败: ${error.message}`);
    }
  }

  /**
   * 模型状态检查
   */
  private async checkModels(): Promise<any> {
    try {
      const models = this.modelManager.listModels();
      const modelStatus = models.map(modelName => {
        const info = this.modelManager.getModelInfo(modelName);
        return {
          name: modelName,
          status: info ? 'loaded' : 'not_loaded',
          trainableParams: info?.trainableParams || 0
        };
      });

      return {
        'models': {
          status: 'up',
          count: models.length,
          details: modelStatus
        }
      };
    } catch (error) {
      throw new Error(`模型检查失败: ${error.message}`);
    }
  }

  /**
   * 服务状态检查
   */
  private async checkServiceStatus(): Promise<any> {
    try {
      return {
        'service': {
          status: 'up',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: '1.0.0'
        }
      };
    } catch (error) {
      throw new Error(`服务状态检查失败: ${error.message}`);
    }
  }

  /**
   * 数据库连接检查
   */
  private async checkDatabaseConnection(): Promise<any> {
    try {
      // 这里应该实际检查数据库连接
      return {
        status: 'up',
        message: '数据库连接正常'
      };
    } catch (error) {
      return {
        status: 'down',
        message: `数据库连接失败: ${error.message}`
      };
    }
  }

  /**
   * Redis连接检查
   */
  private async checkRedisConnection(): Promise<any> {
    try {
      await this.cacheManager.get('health_check');
      return {
        status: 'up',
        message: 'Redis连接正常'
      };
    } catch (error) {
      return {
        status: 'down',
        message: `Redis连接失败: ${error.message}`
      };
    }
  }

  /**
   * 模型状态检查
   */
  private async checkModelStatus(): Promise<any> {
    try {
      const models = this.modelManager.listModels();
      return {
        status: 'up',
        count: models.length,
        models: models
      };
    } catch (error) {
      return {
        status: 'down',
        message: `模型检查失败: ${error.message}`
      };
    }
  }

  /**
   * 计算总体状态
   */
  private calculateOverallStatus(dependencies: any): string {
    const statuses = Object.values(dependencies).map((dep: any) => dep.status);
    
    if (statuses.every(status => status === 'up')) {
      return 'healthy';
    } else if (statuses.some(status => status === 'up')) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }
}
