/**
 * 推荐历史实体
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('recommendation_history')
@Index(['userId', 'timestamp'])
@Index(['itemId', 'timestamp'])
@Index(['algorithm', 'timestamp'])
export class RecommendationHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  itemId: string;

  @Column({ type: 'varchar', length: 100 })
  itemType: string;

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  score: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  confidence: number;

  @Column({ type: 'varchar', length: 100 })
  algorithm: string;

  @Column({ type: 'text', nullable: true })
  explanation: string;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  novelty: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  diversity: number;

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  clicked: boolean;

  @Column({ type: 'boolean', default: false })
  converted: boolean;

  @Column({ type: 'int', nullable: true })
  position: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId: string;

  @CreateDateColumn()
  timestamp: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
