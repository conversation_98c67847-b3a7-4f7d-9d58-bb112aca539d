/**
 * 用户交互实体
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

@Entity('user_interactions')
@Index(['userId', 'timestamp'])
@Index(['itemId', 'timestamp'])
@Index(['action', 'timestamp'])
export class UserInteraction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  itemId: string;

  @Column({ type: 'varchar', length: 100 })
  itemType: string;

  @Column({ type: 'varchar', length: 50 })
  action: string; // view, like, share, download, rate, comment, click

  @Column({ type: 'decimal', precision: 3, scale: 2, nullable: true })
  rating: number; // 1.0 - 5.0

  @Column({ type: 'int', nullable: true })
  duration: number; // 交互持续时间（秒）

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  implicitRating: number; // 隐式评分

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  device: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  platform: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  referrer: string;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @CreateDateColumn()
  timestamp: Date;
}
