/**
 * 用户画像实体
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('user_profiles')
export class UserProfileEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @Index()
  userId: string;

  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  behaviorPatterns: Record<string, any>;

  @Column({ 
    type: 'enum', 
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate'
  })
  skillLevel: string;

  @Column({ type: 'json', nullable: true })
  interests: string[];

  @Column({ type: 'json', nullable: true })
  demographics: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  categoryPreferences: Record<string, number>;

  @Column({ type: 'json', nullable: true })
  featureWeights: Record<string, number>;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  diversityPreference: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  noveltyPreference: number;

  @Column({ type: 'int', default: 0 })
  totalInteractions: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  averageRating: number;

  @Column({ type: 'json', nullable: true })
  recentActivity: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  lastActiveAt: Date;

  @Column({ type: 'int', default: 1 })
  profileVersion: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
