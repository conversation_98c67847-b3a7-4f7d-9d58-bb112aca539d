/**
 * 推荐系统接口定义
 */

export enum RecommendationType {
  ASSET = 'asset',
  SCENE_TEMPLATE = 'scene_template',
  COLLABORATOR = 'collaborator',
  LEARNING_PATH = 'learning_path',
  MATERIAL = 'material',
  COMPONENT = 'component',
  CONTENT = 'content',
  USER = 'user'
}

export interface RecommendationRequest {
  userId: string;
  type: RecommendationType;
  count?: number;
  filters?: Record<string, any>;
  context?: Record<string, any>;
  diversityWeight?: number;
  noveltyWeight?: number;
  includeExplanation?: boolean;
}

export interface RecommendationResponse {
  itemId: string;
  itemType: string;
  score: number;
  confidence?: number;
  algorithm: string;
  explanation?: string;
  novelty?: number;
  diversity?: number;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

export interface UserProfile {
  userId: string;
  preferences: Record<string, any>;
  behaviorPatterns: Record<string, any>;
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  interests: string[];
  demographics?: Record<string, any>;
  activityHistory?: UserActivity[];
}

export interface UserActivity {
  itemId: string;
  itemType: string;
  action: 'view' | 'like' | 'share' | 'download' | 'rate' | 'comment';
  timestamp: Date;
  duration?: number;
  rating?: number;
  context?: Record<string, any>;
}

export interface ContentFeature {
  featureId: string;
  featureName: string;
  featureType: 'categorical' | 'numerical' | 'text' | 'vector';
  value: any;
  weight?: number;
  vector?: number[];
}

export interface RecommendationFeedback {
  userId: string;
  itemId: string;
  recommendationId?: string;
  rating: number; // 1-5 星级评分
  feedbackType: 'explicit' | 'implicit';
  action: 'like' | 'dislike' | 'click' | 'view' | 'skip' | 'share';
  timestamp: Date;
  context?: Record<string, any>;
  comment?: string;
}

export interface RecommendationAlgorithm {
  recommend(request: RecommendationRequest): Promise<RecommendationResponse[]>;
  train?(trainingData: TrainingData): Promise<void>;
  updateModel?(feedback: RecommendationFeedback[]): Promise<void>;
}

export interface TrainingData {
  interactions: UserInteraction[];
  userProfiles?: UserProfile[];
  itemFeatures?: ItemFeature[];
  contextData?: ContextData[];
}

export interface UserInteraction {
  userId: string;
  itemId: string;
  rating: number;
  timestamp: Date;
  action: string;
  context?: Record<string, any>;
}

export interface ItemFeature {
  itemId: string;
  features: ContentFeature[];
  category: string;
  tags: string[];
  metadata: Record<string, any>;
}

export interface ContextData {
  contextId: string;
  userId: string;
  timestamp: Date;
  location?: string;
  device?: string;
  sessionData?: Record<string, any>;
}

export interface ModelConfig {
  embeddingDim: number;
  hiddenLayers: number[];
  dropoutRate: number;
  learningRate: number;
  batchSize?: number;
  epochs?: number;
  validationSplit?: number;
}

export interface RecommendationMetrics {
  precision: number;
  recall: number;
  f1Score: number;
  ndcg: number;
  diversity: number;
  novelty: number;
  coverage: number;
  serendipity: number;
}

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxSize: number; // Maximum cache size
  strategy: 'lru' | 'lfu' | 'fifo';
}

export interface RecommendationConfig {
  algorithms: {
    [key in RecommendationType]: string;
  };
  cache: CacheConfig;
  model: ModelConfig;
  evaluation: {
    metricsEnabled: boolean;
    evaluationInterval: number;
  };
}
