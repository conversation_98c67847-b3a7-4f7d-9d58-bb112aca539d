# DL引擎渲染服务 Makefile

.PHONY: help install build start start-dev start-prod test test-watch test-cov lint format clean docker-build docker-up docker-down docker-logs health-check

# 默认目标
help:
	@echo "DL引擎渲染服务 - 可用命令:"
	@echo ""
	@echo "开发命令:"
	@echo "  install      - 安装依赖"
	@echo "  build        - 构建项目"
	@echo "  start        - 启动服务"
	@echo "  start-dev    - 启动开发模式"
	@echo "  start-prod   - 启动生产模式"
	@echo ""
	@echo "测试命令:"
	@echo "  test         - 运行测试"
	@echo "  test-watch   - 监视模式运行测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  test-e2e     - 运行端到端测试"
	@echo ""
	@echo "代码质量:"
	@echo "  lint         - 运行ESLint检查"
	@echo "  format       - 格式化代码"
	@echo ""
	@echo "Docker命令:"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-up    - 启动Docker容器"
	@echo "  docker-down  - 停止Docker容器"
	@echo "  docker-logs  - 查看Docker日志"
	@echo "  docker-dev   - 启动开发环境Docker"
	@echo ""
	@echo "工具命令:"
	@echo "  health-check - 检查服务健康状态"
	@echo "  clean        - 清理构建文件"
	@echo "  setup        - 初始化项目环境"

# 安装依赖
install:
	@echo "📦 安装依赖..."
	npm install

# 构建项目
build:
	@echo "🔨 构建项目..."
	npm run build

# 启动服务
start:
	@echo "🚀 启动服务..."
	npm start

# 开发模式
start-dev:
	@echo "🔧 启动开发模式..."
	npm run start:dev

# 生产模式
start-prod:
	@echo "🎯 启动生产模式..."
	npm run start:prod

# 运行测试
test:
	@echo "🧪 运行测试..."
	npm test

# 监视模式测试
test-watch:
	@echo "👀 监视模式运行测试..."
	npm run test:watch

# 测试覆盖率
test-cov:
	@echo "📊 运行测试覆盖率..."
	npm run test:cov

# 端到端测试
test-e2e:
	@echo "🔗 运行端到端测试..."
	npm run test:e2e

# 代码检查
lint:
	@echo "🔍 运行ESLint检查..."
	npm run lint

# 格式化代码
format:
	@echo "✨ 格式化代码..."
	npm run format

# 构建Docker镜像
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker build -t dl-engine-render-service .

# 启动Docker容器
docker-up:
	@echo "🐳 启动Docker容器..."
	docker-compose up -d

# 停止Docker容器
docker-down:
	@echo "🐳 停止Docker容器..."
	docker-compose down

# 查看Docker日志
docker-logs:
	@echo "📋 查看Docker日志..."
	docker-compose logs -f render-service

# 开发环境Docker
docker-dev:
	@echo "🐳 启动开发环境Docker..."
	docker-compose -f docker-compose.dev.yml up -d

# 停止开发环境Docker
docker-dev-down:
	@echo "🐳 停止开发环境Docker..."
	docker-compose -f docker-compose.dev.yml down

# 健康检查
health-check:
	@echo "🏥 检查服务健康状态..."
	./scripts/health-check.sh

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	rm -rf dist
	rm -rf node_modules/.cache
	rm -rf coverage
	rm -rf logs/*.log

# 初始化项目环境
setup:
	@echo "⚙️  初始化项目环境..."
	@if [ ! -f .env ]; then \
		echo "📝 复制环境配置文件..."; \
		cp .env.example .env; \
		echo "请编辑 .env 文件配置数据库和Redis连接信息"; \
	fi
	@echo "📦 安装依赖..."
	npm install
	@echo "📁 创建必要目录..."
	mkdir -p renders uploads logs
	@echo "✅ 项目环境初始化完成!"

# 数据库迁移
db-migrate:
	@echo "🗄️  运行数据库迁移..."
	mysql -h localhost -u root -p < src/database/migrations/001-create-render-tables.sql

# 重启服务
restart:
	@echo "🔄 重启服务..."
	npm run start:prod

# 查看日志
logs:
	@echo "📋 查看服务日志..."
	tail -f logs/app-$(shell date +%Y-%m-%d).log

# 监控资源使用
monitor:
	@echo "📊 监控资源使用..."
	docker stats dl-render-service

# 备份数据
backup:
	@echo "💾 备份数据..."
	@mkdir -p backups
	@docker exec dl-render-mysql mysqldump -u root -ppassword dl_engine_render > backups/db-backup-$(shell date +%Y%m%d-%H%M%S).sql
	@echo "数据库备份完成: backups/db-backup-$(shell date +%Y%m%d-%H%M%S).sql"
