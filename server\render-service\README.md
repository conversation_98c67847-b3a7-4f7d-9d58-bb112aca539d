# DL引擎渲染服务

DL（Digital Learning）引擎渲染服务是一个基于NestJS的微服务，负责处理3D场景的渲染任务，支持图像、视频和动画的渲染输出。

## 功能特性

- 🎨 **多种渲染类型**：支持图像、视频、动画渲染
- 🔄 **异步处理**：基于Bull队列的异步任务处理
- 📊 **实时进度**：渲染进度实时更新
- 🔐 **安全认证**：JWT认证和权限控制
- 🚦 **限流保护**：API请求限流防护
- 📝 **完整日志**：详细的操作日志记录
- 🏥 **健康检查**：服务健康状态监控
- 📚 **API文档**：Swagger自动生成的API文档

## 技术栈

- **框架**: NestJS
- **数据库**: MySQL + TypeORM
- **队列**: Redis + Bull
- **认证**: JWT
- **图像处理**: Sharp
- **文档**: Swagger
- **测试**: Jest

## 快速开始

### 环境要求

- Node.js >= 18
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和Redis连接信息。

### 数据库初始化

确保MySQL服务运行，创建数据库：

```sql
CREATE DATABASE dl_engine_render;
```

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

服务将在以下端口启动：
- 微服务端口: 3004
- HTTP API端口: 4004

## API文档

启动服务后，访问 `http://localhost:4004/api/docs` 查看完整的API文档。

## 主要API端点

### 渲染任务管理

- `POST /api/render/jobs` - 创建渲染任务
- `GET /api/render/jobs` - 获取渲染任务列表
- `GET /api/render/jobs/:id` - 获取单个渲染任务
- `POST /api/render/jobs/:id/cancel` - 取消渲染任务
- `DELETE /api/render/jobs/:id` - 删除渲染任务

### 渲染结果

- `GET /api/render/results/:id` - 获取渲染结果信息
- `GET /api/render/results/:id/download` - 下载渲染结果文件

### 健康检查

- `GET /api/health` - 服务健康检查

## 渲染配置

### 图像渲染

```json
{
  "name": "图像渲染任务",
  "type": "image",
  "projectId": "project-uuid",
  "sceneId": "scene-uuid",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": 80,
    "format": "png",
    "camera": "camera-id",
    "lighting": "lighting-setup",
    "postProcessing": true
  }
}
```

### 视频渲染

```json
{
  "name": "视频渲染任务",
  "type": "video",
  "projectId": "project-uuid",
  "sceneId": "scene-uuid",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": 80,
    "format": "mp4",
    "frames": 300,
    "fps": 30,
    "camera": "camera-id",
    "lighting": "lighting-setup"
  }
}
```

## 开发指南

### 项目结构

```
src/
├── auth/                 # 认证模块
│   └── guards/          # 认证守卫
├── common/              # 公共模块
│   ├── filters/         # 异常过滤器
│   ├── guards/          # 守卫
│   └── interceptors/    # 拦截器
├── health/              # 健康检查模块
├── render/              # 渲染模块
│   ├── dto/            # 数据传输对象
│   ├── entities/       # 实体类
│   ├── engines/        # 渲染引擎
│   └── interfaces/     # 接口定义
├── app.module.ts        # 应用主模块
└── main.ts             # 应用入口
```

### 添加新的渲染引擎

1. 实现 `IRenderEngine` 接口
2. 在 `RenderModule` 中注册新引擎
3. 更新 `RenderProcessor` 以使用新引擎

### 运行测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## Docker部署

### 构建镜像

```bash
docker build -t dl-engine-render-service .
```

### 运行容器

```bash
docker run -d \
  --name render-service \
  -p 3004:3004 \
  -p 4004:4004 \
  -e DB_HOST=mysql-host \
  -e REDIS_HOST=redis-host \
  dl-engine-render-service
```

## 监控和日志

### 日志配置

日志文件位置：`./logs/render-service.log`

日志级别可通过环境变量 `LOG_LEVEL` 配置。

### 健康检查

服务提供健康检查端点，可用于负载均衡器和监控系统：

```bash
curl http://localhost:4004/api/health
```

## 性能优化

### 渲染队列配置

- 最大并发任务数：通过 `RENDER_MAX_CONCURRENT_JOBS` 配置
- 任务超时时间：通过 `RENDER_TIMEOUT` 配置

### 限流配置

- 创建渲染任务：每分钟最多10个请求
- 其他API：根据需要调整

## 故障排除

### 常见问题

1. **渲染任务失败**
   - 检查场景数据是否有效
   - 确认渲染参数是否正确
   - 查看错误日志获取详细信息

2. **队列处理缓慢**
   - 检查Redis连接状态
   - 调整并发任务数量
   - 监控系统资源使用情况

3. **文件下载失败**
   - 确认文件路径是否存在
   - 检查文件权限设置
   - 验证用户访问权限

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
