version: '3.8'

services:
  # 开发环境渲染服务
  render-service-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: dl-render-service-dev
    ports:
      - "3004:3004"  # 微服务端口
      - "4004:4004"  # HTTP API端口
      - "9229:9229"  # 调试端口
    environment:
      - NODE_ENV=development
      - DB_HOST=mysql-dev
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=dev_password
      - DB_DATABASE=dl_engine_render_dev
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - JWT_SECRET=dev-secret-key
      - RENDER_OUTPUT_DIR=/app/renders
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - /app/node_modules
      - render_dev_data:/app/renders
      - upload_dev_data:/app/uploads
      - log_dev_data:/app/logs
    depends_on:
      - mysql-dev
      - redis-dev
    restart: unless-stopped
    networks:
      - dl-engine-dev-network
    command: npm run start:debug

  # 开发环境MySQL
  mysql-dev:
    image: mysql:8.0
    container_name: dl-render-mysql-dev
    environment:
      - MYSQL_ROOT_PASSWORD=dev_password
      - MYSQL_DATABASE=dl_engine_render_dev
      - MYSQL_USER=render_dev_user
      - MYSQL_PASSWORD=render_dev_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./src/database/migrations:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    networks:
      - dl-engine-dev-network

  # 开发环境Redis
  redis-dev:
    image: redis:7-alpine
    container_name: dl-render-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - dl-engine-dev-network

  # 开发工具 - Redis管理界面
  redis-commander-dev:
    image: rediscommander/redis-commander:latest
    container_name: dl-render-redis-commander-dev
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8082:8081"
    depends_on:
      - redis-dev
    restart: unless-stopped
    networks:
      - dl-engine-dev-network

  # 开发工具 - MySQL管理界面
  phpmyadmin-dev:
    image: phpmyadmin/phpmyadmin
    container_name: dl-render-phpmyadmin-dev
    environment:
      - PMA_HOST=mysql-dev
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=dev_password
    ports:
      - "8083:80"
    depends_on:
      - mysql-dev
    restart: unless-stopped
    networks:
      - dl-engine-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  render_dev_data:
    driver: local
  upload_dev_data:
    driver: local
  log_dev_data:
    driver: local

networks:
  dl-engine-dev-network:
    driver: bridge
