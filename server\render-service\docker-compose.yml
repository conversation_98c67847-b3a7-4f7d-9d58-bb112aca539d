version: '3.8'

services:
  # 渲染服务
  render-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-render-service
    ports:
      - "3004:3004"  # 微服务端口
      - "4004:4004"  # HTTP API端口
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=dl_engine_render
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-secret-key-here
      - RENDER_OUTPUT_DIR=/app/renders
      - LOG_LEVEL=info
    volumes:
      - render_data:/app/renders
      - upload_data:/app/uploads
      - log_data:/app/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - dl-engine-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: dl-render-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dl_engine_render
      - MYSQL_USER=render_user
      - MYSQL_PASSWORD=render_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/database/migrations:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    networks:
      - dl-engine-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: dl-render-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - dl-engine-network

  # Redis管理界面（可选）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: dl-render-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - dl-engine-network

  # MySQL管理界面（可选）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: dl-render-phpmyadmin
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=password
    ports:
      - "8080:80"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - dl-engine-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  render_data:
    driver: local
  upload_data:
    driver: local
  log_data:
    driver: local

networks:
  dl-engine-network:
    driver: bridge
