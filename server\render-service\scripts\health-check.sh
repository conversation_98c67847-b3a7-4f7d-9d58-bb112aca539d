#!/bin/bash

# DL引擎渲染服务健康检查脚本

set -e

# 配置
HOST=${RENDER_SERVICE_HOST:-localhost}
HTTP_PORT=${RENDER_SERVICE_HTTP_PORT:-4004}
HEALTH_ENDPOINT="http://${HOST}:${HTTP_PORT}/api/health"

echo "🏥 检查渲染服务健康状态..."
echo "检查地址: $HEALTH_ENDPOINT"

# 发送健康检查请求
RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$HEALTH_ENDPOINT" || echo "000")

if [ "$RESPONSE" = "200" ]; then
    echo "✅ 服务健康状态正常"
    
    # 显示详细信息
    if command -v jq &> /dev/null; then
        echo "📊 健康检查详情:"
        cat /tmp/health_response.json | jq .
    else
        echo "📊 健康检查响应:"
        cat /tmp/health_response.json
    fi
    
    exit 0
else
    echo "❌ 服务健康检查失败 (HTTP状态码: $RESPONSE)"
    
    if [ -f /tmp/health_response.json ]; then
        echo "错误响应:"
        cat /tmp/health_response.json
    fi
    
    exit 1
fi
