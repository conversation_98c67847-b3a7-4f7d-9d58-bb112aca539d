#!/bin/bash

# DL引擎渲染服务启动脚本

set -e

echo "🚀 启动DL引擎渲染服务..."

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "Node.js版本: $NODE_VERSION"

# 检查环境变量
if [ ! -f .env ]; then
    echo "⚠️  未找到.env文件，复制.env.example..."
    cp .env.example .env
    echo "📝 请编辑.env文件配置数据库和Redis连接信息"
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 构建项目
echo "🔨 构建项目..."
npm run build

# 创建必要的目录
echo "📁 创建输出目录..."
mkdir -p renders
mkdir -p uploads
mkdir -p logs

# 检查数据库连接
echo "🔍 检查数据库连接..."
# 这里可以添加数据库连接检查逻辑

# 检查Redis连接
echo "🔍 检查Redis连接..."
# 这里可以添加Redis连接检查逻辑

# 启动服务
echo "🎯 启动渲染服务..."
if [ "$NODE_ENV" = "production" ]; then
    npm run start:prod
else
    npm run start:dev
fi
