/**
 * Multer文件上传配置
 */
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { BadRequestException } from '@nestjs/common';

// 允许的文件类型
const ALLOWED_FILE_TYPES = {
  image: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'],
  video: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'],
  model: ['.obj', '.fbx', '.gltf', '.glb', '.dae', '.3ds', '.blend'],
  texture: ['.jpg', '.jpeg', '.png', '.tga', '.exr', '.hdr', '.dds'],
  scene: ['.json', '.xml', '.yaml', '.yml'],
};

// 最大文件大小 (100MB)
const MAX_FILE_SIZE = 100 * 1024 * 1024;

export const multerConfig: MulterOptions = {
  storage: diskStorage({
    destination: (req, file, callback) => {
      const uploadPath = process.env.UPLOAD_DIR || './uploads';
      
      // 根据文件类型创建子目录
      let subDir = 'others';
      const ext = extname(file.originalname).toLowerCase();
      
      for (const [type, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
        if (extensions.includes(ext)) {
          subDir = type;
          break;
        }
      }
      
      const fullPath = join(uploadPath, subDir);
      
      // 确保目录存在
      if (!existsSync(fullPath)) {
        mkdirSync(fullPath, { recursive: true });
      }
      
      callback(null, fullPath);
    },
    
    filename: (req, file, callback) => {
      // 生成唯一文件名
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const ext = extname(file.originalname);
      const name = file.originalname.replace(ext, '').replace(/[^a-zA-Z0-9]/g, '_');
      callback(null, `${name}_${uniqueSuffix}${ext}`);
    },
  }),
  
  fileFilter: (req, file, callback) => {
    const ext = extname(file.originalname).toLowerCase();
    const allAllowedExtensions = Object.values(ALLOWED_FILE_TYPES).flat();
    
    if (!allAllowedExtensions.includes(ext)) {
      return callback(
        new BadRequestException(
          `不支持的文件类型: ${ext}。支持的类型: ${allAllowedExtensions.join(', ')}`
        ),
        false,
      );
    }
    
    callback(null, true);
  },
  
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 10, // 最多10个文件
  },
};

// 特定类型的上传配置
export const imageUploadConfig: MulterOptions = {
  ...multerConfig,
  fileFilter: (req, file, callback) => {
    const ext = extname(file.originalname).toLowerCase();
    
    if (!ALLOWED_FILE_TYPES.image.includes(ext)) {
      return callback(
        new BadRequestException(
          `只支持图像文件: ${ALLOWED_FILE_TYPES.image.join(', ')}`
        ),
        false,
      );
    }
    
    callback(null, true);
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB for images
    files: 5,
  },
};

export const modelUploadConfig: MulterOptions = {
  ...multerConfig,
  fileFilter: (req, file, callback) => {
    const ext = extname(file.originalname).toLowerCase();
    
    if (!ALLOWED_FILE_TYPES.model.includes(ext)) {
      return callback(
        new BadRequestException(
          `只支持3D模型文件: ${ALLOWED_FILE_TYPES.model.join(', ')}`
        ),
        false,
      );
    }
    
    callback(null, true);
  },
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 1,
  },
};
