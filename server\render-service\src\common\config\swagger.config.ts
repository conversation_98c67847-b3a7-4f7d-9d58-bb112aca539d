/**
 * Swagger API文档配置
 */
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('DL引擎渲染服务API')
    .setDescription(`
# DL引擎渲染服务API文档

DL（Digital Learning）引擎渲染服务提供3D场景的渲染功能，支持图像、视频和动画的渲染输出。

## 功能特性

- 🎨 **多种渲染类型**：支持图像、视频、动画渲染
- 🔄 **异步处理**：基于队列的异步任务处理
- 📊 **实时进度**：渲染进度实时更新
- 🔐 **安全认证**：JWT认证和权限控制
- 📁 **文件管理**：支持资源文件上传和结果下载

## 认证方式

所有API端点都需要JWT认证，请在请求头中包含：
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## 渲染流程

1. **创建任务**：通过 \`POST /api/render/jobs\` 创建渲染任务
2. **监控进度**：通过 \`GET /api/render/jobs/{id}\` 查看任务状态和进度
3. **获取结果**：任务完成后通过 \`GET /api/render/results/{id}\` 获取结果
4. **下载文件**：通过 \`GET /api/render/results/{id}/download\` 下载渲染文件

## 错误处理

API使用标准HTTP状态码：
- \`200\` - 成功
- \`201\` - 创建成功
- \`400\` - 请求参数错误
- \`401\` - 未认证
- \`403\` - 权限不足
- \`404\` - 资源不存在
- \`429\` - 请求过于频繁
- \`500\` - 服务器内部错误

## 限流规则

- 创建渲染任务：每分钟最多10个请求
- 重试任务：每分钟最多5个请求
- 其他API：根据具体端点设置

## 支持的文件格式

### 图像渲染
- PNG, JPG, JPEG, WebP, TIFF

### 视频渲染
- MP4, AVI, MOV, WebM

### 动画渲染
- GIF, WebP, MP4
    `)
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('渲染任务', '渲染任务的创建、查询、管理')
    .addTag('渲染结果', '渲染结果的查询和下载')
    .addTag('统计信息', '渲染统计和队列状态')
    .addTag('健康检查', '服务健康状态检查')
    .addTag('文件上传', '资源文件上传管理')
    .addServer('http://localhost:4004', '开发环境')
    .addServer('https://api.dl-engine.com', '生产环境')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  });

  // 自定义CSS样式
  const customCss = `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info .title { color: #1976d2; }
    .swagger-ui .scheme-container { background: #fafafa; padding: 15px; }
    .swagger-ui .info .description p { margin: 10px 0; }
    .swagger-ui .info .description h1 { color: #1976d2; font-size: 1.5em; }
    .swagger-ui .info .description h2 { color: #424242; font-size: 1.2em; }
    .swagger-ui .info .description code { 
      background: #f5f5f5; 
      padding: 2px 4px; 
      border-radius: 3px; 
      font-family: 'Monaco', 'Consolas', monospace;
    }
  `;

  SwaggerModule.setup('api/docs', app, document, {
    customCss,
    customSiteTitle: 'DL引擎渲染服务API文档',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      docExpansion: 'none',
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2,
    },
  });
}
