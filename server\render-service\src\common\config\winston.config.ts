/**
 * Winston日志配置
 */
import { LoggerService } from '@nestjs/common';

/**
 * 简单的日志服务实现
 */
export class CustomLogger implements LoggerService {
  private logLevel: string;

  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'info';
  }

  log(message: any, context?: string) {
    this.writeLog('LOG', message, context);
  }

  error(message: any, trace?: string, context?: string) {
    this.writeLog('ERROR', message, context, trace);
  }

  warn(message: any, context?: string) {
    this.writeLog('WARN', message, context);
  }

  debug(message: any, context?: string) {
    if (this.logLevel === 'debug') {
      this.writeLog('DEBUG', message, context);
    }
  }

  verbose(message: any, context?: string) {
    if (this.logLevel === 'debug' || this.logLevel === 'verbose') {
      this.writeLog('VERBOSE', message, context);
    }
  }

  private writeLog(level: string, message: any, context?: string, trace?: string) {
    const timestamp = new Date().toISOString();
    const contextStr = context ? `[${context}] ` : '';
    const messageStr = typeof message === 'object' ? JSON.stringify(message) : message;
    const traceStr = trace ? `\n${trace}` : '';

    console.log(`${timestamp} [${level}] ${contextStr}${messageStr}${traceStr}`);
  }
}

export const createWinstonLogger = () => {
  return new CustomLogger();
};
