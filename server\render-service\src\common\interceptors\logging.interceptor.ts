/**
 * 日志拦截器
 */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { method, url, body, query, params } = request;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip;

    const now = Date.now();

    this.logger.log(
      `请求开始: ${method} ${url} - ${ip} - ${userAgent}`,
    );

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const contentLength = response.get('content-length');
        const delay = Date.now() - now;

        this.logger.log(
          `请求完成: ${method} ${url} ${statusCode} ${contentLength} - ${delay}ms`,
        );

        // 记录详细的请求信息（仅在开发环境）
        if (process.env.NODE_ENV === 'development') {
          this.logger.debug(
            `请求详情: ${JSON.stringify({
              method,
              url,
              statusCode,
              delay: `${delay}ms`,
              body: method !== 'GET' ? body : undefined,
              query: Object.keys(query).length > 0 ? query : undefined,
              params: Object.keys(params).length > 0 ? params : undefined,
            })}`,
          );
        }
      }),
    );
  }
}
