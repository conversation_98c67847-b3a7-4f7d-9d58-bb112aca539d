-- 创建渲染任务表
CREATE TABLE IF NOT EXISTS `render_jobs` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `type` enum('image','video','animation') NOT NULL DEFAULT 'image',
  `status` enum('pending','processing','completed','failed','canceled') NOT NULL DEFAULT 'pending',
  `userId` varchar(36) NOT NULL,
  `projectId` varchar(36) NOT NULL,
  `sceneId` varchar(36) NOT NULL,
  `settings` json NOT NULL,
  `errorMessage` text,
  `progress` int DEFAULT 0,
  `estimatedTimeRemaining` int DEFAULT NULL,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY <PERSON>Y (`id`),
  KEY `idx_render_jobs_userId` (`userId`),
  KEY `idx_render_jobs_projectId` (`projectId`),
  <PERSON>EY `idx_render_jobs_sceneId` (`sceneId`),
  KEY `idx_render_jobs_status` (`status`),
  KEY `idx_render_jobs_type` (`type`),
  KEY `idx_render_jobs_createdAt` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建渲染结果表
CREATE TABLE IF NOT EXISTS `render_results` (
  `id` varchar(36) NOT NULL,
  `fileUrl` varchar(500) NOT NULL,
  `thumbnailUrl` varchar(500) DEFAULT NULL,
  `fileSize` bigint DEFAULT NULL,
  `width` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `format` varchar(50) DEFAULT NULL,
  `duration` float DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `jobId` varchar(36) NOT NULL,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_render_results_jobId` (`jobId`),
  KEY `idx_render_results_createdAt` (`createdAt`),
  CONSTRAINT `fk_render_results_jobId` FOREIGN KEY (`jobId`) REFERENCES `render_jobs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引以优化查询性能
CREATE INDEX `idx_render_jobs_user_status` ON `render_jobs` (`userId`, `status`);
CREATE INDEX `idx_render_jobs_user_type` ON `render_jobs` (`userId`, `type`);
CREATE INDEX `idx_render_jobs_project_scene` ON `render_jobs` (`projectId`, `sceneId`);
CREATE INDEX `idx_render_jobs_status_created` ON `render_jobs` (`status`, `createdAt`);

-- 创建全文索引用于搜索
ALTER TABLE `render_jobs` ADD FULLTEXT(`name`, `description`);
