/**
 * 渲染服务入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { setupSwagger } from './common/config/swagger.config';
import { createWinstonLogger } from './common/config/winston.config';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule, {
    logger: createWinstonLogger(),
  });
  const configService = app.get(ConfigService);
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('RENDER_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('RENDER_SERVICE_PORT', 3004),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors();
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet());
  
  // Swagger文档
  setupSwagger(app);
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const httpPort = configService.get<number>('RENDER_SERVICE_HTTP_PORT', 4004);
  await app.listen(httpPort);
  console.log(`渲染服务已启动，微服务端口: ${configService.get<number>('RENDER_SERVICE_PORT', 3004)}, HTTP端口: ${httpPort}`);
}

bootstrap();
