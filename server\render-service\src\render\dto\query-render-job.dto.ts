/**
 * 查询渲染任务DTO
 */
import { IsOptional, IsEnum, IsString, IsNumber, Min, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { RenderJobStatus, RenderJobType } from '../entities/render-job.entity';

export class QueryRenderJobDto {
  @ApiProperty({ description: '任务状态', enum: RenderJobStatus, required: false })
  @IsEnum(RenderJobStatus)
  @IsOptional()
  status?: RenderJobStatus;

  @ApiProperty({ description: '渲染类型', enum: RenderJobType, required: false })
  @IsEnum(RenderJobType)
  @IsOptional()
  type?: RenderJobType;

  @ApiProperty({ description: '项目ID', required: false })
  @IsString()
  @IsOptional()
  projectId?: string;

  @ApiProperty({ description: '场景ID', required: false })
  @IsString()
  @IsOptional()
  sceneId?: string;

  @ApiProperty({ description: '页码', minimum: 1, default: 1, required: false })
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ description: '每页数量', minimum: 1, maximum: 100, default: 10, required: false })
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({ description: '排序字段', default: 'createdAt', required: false })
  @IsString()
  @IsOptional()
  sortBy?: string = 'createdAt';

  @ApiProperty({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC', required: false })
  @IsEnum(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({ description: '开始日期', required: false })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({ description: '结束日期', required: false })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;
}
