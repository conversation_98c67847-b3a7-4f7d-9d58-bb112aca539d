/**
 * 更新渲染任务DTO
 */
import { IsString, IsOptional, IsEnum, IsNumber, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RenderJobStatus } from '../entities/render-job.entity';

export class UpdateRenderJobDto {
  @ApiProperty({ description: '任务名称', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '任务描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateRenderJobStatusDto {
  @ApiProperty({ description: '任务状态', enum: RenderJobStatus })
  @IsEnum(RenderJobStatus)
  status: RenderJobStatus;

  @ApiProperty({ description: '进度百分比', minimum: 0, maximum: 100, required: false })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  progress?: number;

  @ApiProperty({ description: '错误信息', required: false })
  @IsString()
  @IsOptional()
  errorMessage?: string;

  @ApiProperty({ description: '预计剩余时间（毫秒）', required: false })
  @IsNumber()
  @IsOptional()
  estimatedTimeRemaining?: number;
}
