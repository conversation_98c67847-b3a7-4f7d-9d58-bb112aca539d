/**
 * 默认渲染引擎实现
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs';
import { IRenderEngine, RenderOptions, RenderProgress, RenderOutput } from '../interfaces/render-engine.interface';
import { RenderJobType } from '../entities/render-job.entity';

@Injectable()
export class DefaultRenderEngine implements IRenderEngine {
  private readonly logger = new Logger(DefaultRenderEngine.name);
  private readonly outputDir: string;
  private readonly activeJobs = new Map<string, boolean>();

  constructor(private readonly configService: ConfigService) {
    this.outputDir = this.configService.get<string>('RENDER_OUTPUT_DIR', './renders');
    this.ensureOutputDir();
  }

  async renderImage(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput> {
    const jobId = this.generateJobId();
    this.activeJobs.set(jobId, true);

    try {
      this.logger.log(`开始渲染图像: ${jobId}`);
      
      // 创建输出目录
      const jobOutputDir = path.join(this.outputDir, jobId);
      if (!fs.existsSync(jobOutputDir)) {
        fs.mkdirSync(jobOutputDir, { recursive: true });
      }

      // 模拟渲染进度
      const stages = [
        { stage: '初始化场景', progress: 10 },
        { stage: '加载资源', progress: 30 },
        { stage: '设置光照', progress: 50 },
        { stage: '渲染几何体', progress: 70 },
        { stage: '后期处理', progress: 90 },
        { stage: '保存文件', progress: 100 },
      ];

      for (const { stage, progress } of stages) {
        if (!this.activeJobs.get(jobId)) {
          throw new Error('渲染任务已取消');
        }

        onProgress?.({
          progress,
          stage,
          estimatedTimeRemaining: ((100 - progress) / 100) * 10000, // 估算剩余时间
        });

        // 模拟处理时间
        await this.sleep(1000);
      }

      // 生成示例图像
      const outputFile = path.join(jobOutputDir, `render.${options.format || 'png'}`);
      const thumbnailFile = path.join(jobOutputDir, `thumbnail.${options.format || 'png'}`);

      await this.generateSampleImage(outputFile, options.width, options.height);
      await this.generateThumbnail(outputFile, thumbnailFile);

      const stats = fs.statSync(outputFile);

      return {
        fileUrl: outputFile,
        thumbnailUrl: thumbnailFile,
        fileSize: stats.size,
        width: options.width,
        height: options.height,
        format: options.format || 'png',
        metadata: {
          renderTime: 6000,
          quality: options.quality,
          engine: 'default',
          sceneComplexity: this.calculateSceneComplexity(sceneData),
        },
      };
    } finally {
      this.activeJobs.delete(jobId);
    }
  }

  async renderVideo(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput> {
    const jobId = this.generateJobId();
    this.activeJobs.set(jobId, true);

    try {
      this.logger.log(`开始渲染视频: ${jobId}`);
      
      const jobOutputDir = path.join(this.outputDir, jobId);
      if (!fs.existsSync(jobOutputDir)) {
        fs.mkdirSync(jobOutputDir, { recursive: true });
      }

      // 模拟视频渲染进度
      const totalFrames = options.frames || 30;
      for (let frame = 1; frame <= totalFrames; frame++) {
        if (!this.activeJobs.get(jobId)) {
          throw new Error('渲染任务已取消');
        }

        const progress = (frame / totalFrames) * 100;
        onProgress?.({
          progress,
          stage: `渲染帧 ${frame}/${totalFrames}`,
          estimatedTimeRemaining: ((totalFrames - frame) / totalFrames) * 30000,
        });

        await this.sleep(500);
      }

      // 生成示例视频文件
      const outputFile = path.join(jobOutputDir, `render.${options.format || 'mp4'}`);
      const thumbnailFile = path.join(jobOutputDir, `thumbnail.png`);

      // 创建示例视频文件
      fs.writeFileSync(outputFile, 'Sample video content');
      await this.generateSampleImage(thumbnailFile, 320, 240);

      const stats = fs.statSync(outputFile);

      return {
        fileUrl: outputFile,
        thumbnailUrl: thumbnailFile,
        fileSize: stats.size,
        width: options.width,
        height: options.height,
        format: options.format || 'mp4',
        duration: (options.frames || 30) / (options.fps || 30),
        metadata: {
          renderTime: totalFrames * 500,
          quality: options.quality,
          fps: options.fps || 30,
          frames: options.frames || 30,
          engine: 'default',
        },
      };
    } finally {
      this.activeJobs.delete(jobId);
    }
  }

  async renderAnimation(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput> {
    // 动画渲染类似于视频渲染，但输出格式通常是GIF
    const result = await this.renderVideo(sceneData, {
      ...options,
      format: options.format || 'gif',
    }, onProgress);

    return {
      ...result,
      format: options.format || 'gif',
    };
  }

  async cancelRender(jobId: string): Promise<void> {
    this.activeJobs.set(jobId, false);
    this.logger.log(`取消渲染任务: ${jobId}`);
  }

  getSupportedFormats(type: RenderJobType): string[] {
    switch (type) {
      case RenderJobType.IMAGE:
        return ['png', 'jpg', 'jpeg', 'webp', 'tiff'];
      case RenderJobType.VIDEO:
        return ['mp4', 'avi', 'mov', 'webm'];
      case RenderJobType.ANIMATION:
        return ['gif', 'webp', 'mp4'];
      default:
        return [];
    }
  }

  validateOptions(type: RenderJobType, options: RenderOptions): boolean {
    // 基本验证
    if (!options.width || !options.height || options.width <= 0 || options.height <= 0) {
      return false;
    }

    if (options.quality < 1 || options.quality > 100) {
      return false;
    }

    const supportedFormats = this.getSupportedFormats(type);
    if (options.format && !supportedFormats.includes(options.format)) {
      return false;
    }

    // 视频和动画特定验证
    if (type === RenderJobType.VIDEO || type === RenderJobType.ANIMATION) {
      if (options.frames && options.frames <= 0) {
        return false;
      }
      if (options.fps && (options.fps <= 0 || options.fps > 120)) {
        return false;
      }
    }

    return true;
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async generateSampleImage(outputPath: string, width: number, height: number): Promise<void> {
    try {
      // 尝试动态导入sharp
      const sharp = await import('sharp');
      await sharp.default({
        create: {
          width,
          height,
          channels: 3,
          background: { r: 100, g: 150, b: 200 }
        }
      })
      .png()
      .toFile(outputPath);
    } catch (error) {
      // 如果sharp不可用，创建简单的示例文件
      const sampleContent = `Sample render output ${width}x${height}\nGenerated at: ${new Date().toISOString()}`;
      fs.writeFileSync(outputPath, sampleContent);
    }
  }

  private async generateThumbnail(inputPath: string, outputPath: string): Promise<void> {
    try {
      // 尝试动态导入sharp
      const sharp = await import('sharp');
      await sharp.default(inputPath)
        .resize(320, 240)
        .png()
        .toFile(outputPath);
    } catch (error) {
      // 如果处理失败，复制原文件或创建简单缩略图
      try {
        fs.copyFileSync(inputPath, outputPath);
      } catch (copyError) {
        // 如果复制也失败，创建简单的缩略图文件
        fs.writeFileSync(outputPath, 'Thumbnail placeholder');
      }
    }
  }

  private calculateSceneComplexity(sceneData: any): number {
    // 简单的场景复杂度计算
    if (!sceneData) return 1;
    
    let complexity = 1;
    if (sceneData.objects) complexity += sceneData.objects.length * 0.1;
    if (sceneData.lights) complexity += sceneData.lights.length * 0.2;
    if (sceneData.materials) complexity += sceneData.materials.length * 0.05;
    
    return Math.min(complexity, 10); // 限制在1-10之间
  }

  private ensureOutputDir(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }
}
