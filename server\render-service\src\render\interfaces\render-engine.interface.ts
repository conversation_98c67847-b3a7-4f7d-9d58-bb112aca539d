/**
 * 渲染引擎接口
 */
import { RenderJob, RenderJobType } from '../entities/render-job.entity';

export interface RenderOptions {
  width: number;
  height: number;
  quality: number;
  format: string;
  frames?: number;
  fps?: number;
  camera?: string;
  lighting?: string;
  postProcessing?: boolean;
  [key: string]: any;
}

export interface RenderProgress {
  progress: number;
  stage: string;
  estimatedTimeRemaining?: number;
  message?: string;
}

export interface RenderOutput {
  fileUrl: string;
  thumbnailUrl?: string;
  fileSize: number;
  width: number;
  height: number;
  format: string;
  duration?: number;
  metadata: Record<string, any>;
}

export interface IRenderEngine {
  /**
   * 渲染图像
   */
  renderImage(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput>;

  /**
   * 渲染视频
   */
  renderVideo(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput>;

  /**
   * 渲染动画
   */
  renderAnimation(
    sceneData: any,
    options: RenderOptions,
    onProgress?: (progress: RenderProgress) => void,
  ): Promise<RenderOutput>;

  /**
   * 取消渲染
   */
  cancelRender(jobId: string): Promise<void>;

  /**
   * 获取支持的格式
   */
  getSupportedFormats(type: RenderJobType): string[];

  /**
   * 验证渲染选项
   */
  validateOptions(type: RenderJobType, options: RenderOptions): boolean;
}
