/**
 * 渲染任务事件监听器
 */
import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { RenderJob, RenderJobStatus } from '../entities/render-job.entity';

export interface RenderJobEvent {
  job: RenderJob;
  previousStatus?: RenderJobStatus;
  metadata?: Record<string, any>;
}

@Injectable()
export class RenderJobListener {
  private readonly logger = new Logger(RenderJobListener.name);

  constructor(
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  @OnEvent('render.job.created')
  async handleJobCreated(event: RenderJobEvent) {
    this.logger.log(`渲染任务已创建: ${event.job.id}`);
    
    // 发送通知给用户服务
    try {
      await this.userService.emit('notification.send', {
        userId: event.job.userId,
        type: 'render_job_created',
        title: '渲染任务已创建',
        message: `您的渲染任务"${event.job.name}"已创建并加入队列`,
        data: {
          jobId: event.job.id,
          jobName: event.job.name,
        },
      });
    } catch (error) {
      this.logger.error('发送任务创建通知失败', error);
    }
  }

  @OnEvent('render.job.started')
  async handleJobStarted(event: RenderJobEvent) {
    this.logger.log(`渲染任务开始处理: ${event.job.id}`);
    
    try {
      await this.userService.emit('notification.send', {
        userId: event.job.userId,
        type: 'render_job_started',
        title: '渲染任务开始处理',
        message: `您的渲染任务"${event.job.name}"开始处理`,
        data: {
          jobId: event.job.id,
          jobName: event.job.name,
        },
      });
    } catch (error) {
      this.logger.error('发送任务开始通知失败', error);
    }
  }

  @OnEvent('render.job.completed')
  async handleJobCompleted(event: RenderJobEvent) {
    this.logger.log(`渲染任务完成: ${event.job.id}`);
    
    try {
      await this.userService.emit('notification.send', {
        userId: event.job.userId,
        type: 'render_job_completed',
        title: '渲染任务完成',
        message: `您的渲染任务"${event.job.name}"已完成`,
        data: {
          jobId: event.job.id,
          jobName: event.job.name,
          results: event.job.results,
        },
      });

      // 更新项目统计信息
      await this.projectService.emit('project.render.completed', {
        projectId: event.job.projectId,
        sceneId: event.job.sceneId,
        jobId: event.job.id,
        renderTime: event.metadata?.renderTime,
      });
    } catch (error) {
      this.logger.error('发送任务完成通知失败', error);
    }
  }

  @OnEvent('render.job.failed')
  async handleJobFailed(event: RenderJobEvent) {
    this.logger.log(`渲染任务失败: ${event.job.id}`);
    
    try {
      await this.userService.emit('notification.send', {
        userId: event.job.userId,
        type: 'render_job_failed',
        title: '渲染任务失败',
        message: `您的渲染任务"${event.job.name}"处理失败: ${event.job.errorMessage}`,
        data: {
          jobId: event.job.id,
          jobName: event.job.name,
          errorMessage: event.job.errorMessage,
        },
      });
    } catch (error) {
      this.logger.error('发送任务失败通知失败', error);
    }
  }

  @OnEvent('render.job.canceled')
  async handleJobCanceled(event: RenderJobEvent) {
    this.logger.log(`渲染任务已取消: ${event.job.id}`);
    
    try {
      await this.userService.emit('notification.send', {
        userId: event.job.userId,
        type: 'render_job_canceled',
        title: '渲染任务已取消',
        message: `您的渲染任务"${event.job.name}"已取消`,
        data: {
          jobId: event.job.id,
          jobName: event.job.name,
        },
      });
    } catch (error) {
      this.logger.error('发送任务取消通知失败', error);
    }
  }

  @OnEvent('render.job.progress')
  async handleJobProgress(event: RenderJobEvent & { progress: number }) {
    // 只在特定进度点发送通知（避免过于频繁）
    const milestones = [25, 50, 75];
    if (milestones.includes(event.progress)) {
      this.logger.log(`渲染任务进度更新: ${event.job.id} - ${event.progress}%`);
      
      try {
        await this.userService.emit('notification.send', {
          userId: event.job.userId,
          type: 'render_job_progress',
          title: '渲染进度更新',
          message: `您的渲染任务"${event.job.name}"已完成 ${event.progress}%`,
          data: {
            jobId: event.job.id,
            jobName: event.job.name,
            progress: event.progress,
          },
        });
      } catch (error) {
        this.logger.error('发送进度通知失败', error);
      }
    }
  }
}
