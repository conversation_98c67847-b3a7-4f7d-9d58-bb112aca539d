/**
 * 渲染控制器测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { RenderController } from './render.controller';
import { RenderService } from './render.service';
import { RenderJob, RenderJobStatus, RenderJobType } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { CreateRenderJobDto } from './dto/create-render-job.dto';

describe('RenderController', () => {
  let controller: RenderController;
  let service: RenderService;

  const mockRenderService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    cancel: jest.fn(),
    remove: jest.fn(),
    getResult: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RenderController],
      providers: [
        {
          provide: RenderService,
          useValue: mockRenderService,
        },
      ],
    }).compile();

    controller = module.get<RenderController>(RenderController);
    service = module.get<RenderService>(RenderService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a render job', async () => {
      const userId = 'user-123';
      const createRenderJobDto: CreateRenderJobDto = {
        name: '测试渲染',
        type: RenderJobType.IMAGE,
        projectId: 'project-123',
        sceneId: 'scene-123',
        settings: {
          width: 1920,
          height: 1080,
          quality: 80,
          format: 'png',
        },
      };

      const mockJob: RenderJob = {
        id: 'job-123',
        ...createRenderJobDto,
        userId,
        status: RenderJobStatus.PENDING,
        progress: 0,
        estimatedTimeRemaining: null,
        errorMessage: null,
        results: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockRequest = { user: { id: userId } };
      mockRenderService.create.mockResolvedValue(mockJob);

      const result = await controller.create(mockRequest, createRenderJobDto);

      expect(result).toEqual(mockJob);
      expect(service.create).toHaveBeenCalledWith(userId, createRenderJobDto);
    });
  });

  describe('findAll', () => {
    it('should return all render jobs', async () => {
      const userId = 'user-123';
      const mockJobs: RenderJob[] = [
        {
          id: 'job-1',
          name: '渲染1',
          description: null,
          type: RenderJobType.IMAGE,
          status: RenderJobStatus.COMPLETED,
          userId,
          projectId: 'project-123',
          sceneId: 'scene-123',
          settings: { width: 1920, height: 1080, quality: 80, format: 'png' },
          errorMessage: null,
          progress: 100,
          estimatedTimeRemaining: null,
          results: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockRequest = { user: { id: userId } };
      mockRenderService.findAll.mockResolvedValue(mockJobs);

      const result = await controller.findAll(mockRequest);

      expect(result).toEqual(mockJobs);
      expect(service.findAll).toHaveBeenCalledWith(userId, undefined, undefined);
    });

    it('should return filtered render jobs', async () => {
      const userId = 'user-123';
      const status = RenderJobStatus.COMPLETED;
      const type = RenderJobType.IMAGE;
      const mockJobs: RenderJob[] = [];

      const mockRequest = { user: { id: userId } };
      mockRenderService.findAll.mockResolvedValue(mockJobs);

      const result = await controller.findAll(mockRequest, status, type);

      expect(result).toEqual(mockJobs);
      expect(service.findAll).toHaveBeenCalledWith(userId, status, type);
    });
  });

  describe('findOne', () => {
    it('should return a render job by id', async () => {
      const jobId = 'job-123';
      const userId = 'user-123';
      const mockJob: RenderJob = {
        id: jobId,
        name: '测试渲染',
        description: null,
        type: RenderJobType.IMAGE,
        status: RenderJobStatus.COMPLETED,
        userId,
        projectId: 'project-123',
        sceneId: 'scene-123',
        settings: { width: 1920, height: 1080, quality: 80, format: 'png' },
        errorMessage: null,
        progress: 100,
        estimatedTimeRemaining: null,
        results: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockRequest = { user: { id: userId } };
      mockRenderService.findOne.mockResolvedValue(mockJob);

      const result = await controller.findOne(jobId, mockRequest);

      expect(result).toEqual(mockJob);
      expect(service.findOne).toHaveBeenCalledWith(jobId, userId);
    });
  });

  describe('cancel', () => {
    it('should cancel a render job', async () => {
      const jobId = 'job-123';
      const userId = 'user-123';
      const mockJob: RenderJob = {
        id: jobId,
        name: '测试渲染',
        description: null,
        type: RenderJobType.IMAGE,
        status: RenderJobStatus.CANCELED,
        userId,
        projectId: 'project-123',
        sceneId: 'scene-123',
        settings: { width: 1920, height: 1080, quality: 80, format: 'png' },
        errorMessage: null,
        progress: 50,
        estimatedTimeRemaining: null,
        results: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockRequest = { user: { id: userId } };
      mockRenderService.cancel.mockResolvedValue(mockJob);

      const result = await controller.cancel(jobId, mockRequest);

      expect(result).toEqual(mockJob);
      expect(service.cancel).toHaveBeenCalledWith(jobId, userId);
    });
  });

  describe('remove', () => {
    it('should remove a render job', async () => {
      const jobId = 'job-123';
      const userId = 'user-123';
      const mockRequest = { user: { id: userId } };

      mockRenderService.remove.mockResolvedValue(undefined);

      await controller.remove(jobId, mockRequest);

      expect(service.remove).toHaveBeenCalledWith(jobId, userId);
    });
  });

  describe('getResult', () => {
    it('should return render result', async () => {
      const resultId = 'result-123';
      const userId = 'user-123';
      const mockResult: RenderResult = {
        id: resultId,
        fileUrl: '/path/to/file.png',
        thumbnailUrl: '/path/to/thumbnail.png',
        fileSize: 1024,
        width: 1920,
        height: 1080,
        format: 'png',
        duration: null,
        metadata: {},
        job: null,
        jobId: 'job-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockRequest = { user: { id: userId } };
      mockRenderService.getResult.mockResolvedValue(mockResult);

      const result = await controller.getResult(resultId, mockRequest);

      expect(result).toEqual(mockResult);
      expect(service.getResult).toHaveBeenCalledWith(resultId, userId);
    });
  });
});
