/**
 * 渲染控制器
 */
import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request, Query, Res, StreamableFile, Put, Patch, UseInterceptors, UploadedFiles } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import * as fs from 'fs';
import { RenderService } from './render.service';
import { CreateRenderJobDto } from './dto/create-render-job.dto';
import { UpdateRenderJobDto, UpdateRenderJobStatusDto } from './dto/update-render-job.dto';
import { QueryRenderJobDto } from './dto/query-render-job.dto';
import { RenderJob, RenderJobStatus, RenderJobType } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Throttle } from '../common/guards/throttle.guard';
import { multerConfig } from '../common/config/multer.config';

@ApiTags('渲染')
@Controller('render')
export class RenderController {
  constructor(private readonly renderService: RenderService) {}

  @Post('jobs')
  @UseGuards(JwtAuthGuard)
  @Throttle(10, 60) // 每分钟最多10个渲染任务
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建渲染任务' })
  @ApiResponse({ status: 201, description: '渲染任务创建成功', type: RenderJob })
  async create(@Request() req, @Body() createRenderJobDto: CreateRenderJobDto): Promise<RenderJob> {
    return this.renderService.create(req.user.id, createRenderJobDto);
  }

  @Get('jobs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染任务列表' })
  @ApiResponse({ status: 200, description: '返回渲染任务列表', type: [RenderJob] })
  @ApiQuery({ name: 'status', enum: RenderJobStatus, required: false })
  @ApiQuery({ name: 'type', enum: RenderJobType, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  async findAll(
    @Request() req,
    @Query() query: QueryRenderJobDto,
  ): Promise<{ data: RenderJob[]; total: number; page: number; limit: number }> {
    return this.renderService.findAllPaginated(req.user.id, query);
  }

  @Get('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取渲染任务' })
  @ApiResponse({ status: 200, description: '返回渲染任务信息', type: RenderJob })
  async findOne(@Param('id') id: string, @Request() req): Promise<RenderJob> {
    return this.renderService.findOne(id, req.user.id);
  }

  @Post('jobs/:id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '取消渲染任务' })
  @ApiResponse({ status: 200, description: '渲染任务取消成功', type: RenderJob })
  async cancel(@Param('id') id: string, @Request() req): Promise<RenderJob> {
    return this.renderService.cancel(id, req.user.id);
  }

  @Put('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新渲染任务' })
  @ApiResponse({ status: 200, description: '渲染任务更新成功', type: RenderJob })
  async update(
    @Param('id') id: string,
    @Request() req,
    @Body() updateRenderJobDto: UpdateRenderJobDto,
  ): Promise<RenderJob> {
    return this.renderService.update(id, req.user.id, updateRenderJobDto);
  }

  @Patch('jobs/:id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新渲染任务状态' })
  @ApiResponse({ status: 200, description: '任务状态更新成功', type: RenderJob })
  async updateStatus(
    @Param('id') id: string,
    @Request() req,
    @Body() updateStatusDto: UpdateRenderJobStatusDto,
  ): Promise<RenderJob> {
    return this.renderService.updateStatus(
      id,
      updateStatusDto.status,
      updateStatusDto.progress,
      updateStatusDto.errorMessage,
    );
  }

  @Delete('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除渲染任务' })
  @ApiResponse({ status: 204, description: '渲染任务删除成功' })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.renderService.remove(id, req.user.id);
  }

  @Get('results/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染结果' })
  @ApiResponse({ status: 200, description: '返回渲染结果信息', type: RenderResult })
  async getResult(@Param('id') id: string, @Request() req): Promise<RenderResult> {
    return this.renderService.getResult(id, req.user.id);
  }

  @Get('results/:id/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载渲染结果文件' })
  @ApiResponse({ status: 200, description: '返回渲染结果文件' })
  async downloadResult(
    @Param('id') id: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    const result = await this.renderService.getResult(id, req.user.id);
    
    const file = fs.createReadStream(result.fileUrl);
    const fileName = result.fileUrl.split('/').pop();
    
    res.set({
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Type': result.format === 'png' ? 'image/png' : 
                     result.format === 'jpg' || result.format === 'jpeg' ? 'image/jpeg' : 
                     result.format === 'mp4' ? 'video/mp4' : 'application/octet-stream',
    });
    
    return new StreamableFile(file);
  }

  @Post('jobs/:id/assets')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FilesInterceptor('files', 10, multerConfig))
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传渲染任务相关资源文件' })
  @ApiResponse({ status: 201, description: '文件上传成功' })
  async uploadAssets(
    @Param('id') id: string,
    @Request() req,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<{ message: string; files: any[] }> {
    return this.renderService.uploadAssets(id, req.user.id, files);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染统计信息' })
  @ApiResponse({ status: 200, description: '返回渲染统计信息' })
  async getStats(@Request() req): Promise<any> {
    return this.renderService.getStats(req.user.id);
  }

  @Get('queue/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染队列状态' })
  @ApiResponse({ status: 200, description: '返回队列状态信息' })
  async getQueueStatus(@Request() req): Promise<any> {
    return this.renderService.getQueueStatus();
  }

  @Post('jobs/:id/retry')
  @UseGuards(JwtAuthGuard)
  @Throttle(5, 60) // 每分钟最多5次重试
  @ApiBearerAuth()
  @ApiOperation({ summary: '重试失败的渲染任务' })
  @ApiResponse({ status: 200, description: '任务重试成功', type: RenderJob })
  async retry(@Param('id') id: string, @Request() req): Promise<RenderJob> {
    return this.renderService.retry(id, req.user.id);
  }
}
