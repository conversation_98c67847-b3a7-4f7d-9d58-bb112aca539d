/**
 * 渲染模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RenderController } from './render.controller';
import { RenderService } from './render.service';
import { RenderJob } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { RenderProcessor } from './render.processor';
import { DefaultRenderEngine } from './engines/default-render.engine';

@Module({
  imports: [
    TypeOrmModule.forFeature([RenderJob, RenderResult]),
    BullModule.registerQueue({
      name: 'render',
    }),
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'PROJECT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('PROJECT_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('PROJECT_SERVICE_PORT', 3002),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'ASSET_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('ASSET_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('ASSET_SERVICE_PORT', 3003),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [RenderController],
  providers: [RenderService, RenderProcessor, DefaultRenderEngine],
})
export class RenderModule {}
