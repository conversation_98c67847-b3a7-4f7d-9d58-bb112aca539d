/**
 * 渲染模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
// import { ScheduleModule } from '@nestjs/schedule'; // 暂时注释掉
// import { EventEmitterModule } from '@nestjs/event-emitter'; // 暂时注释掉
import { RenderController } from './render.controller';
import { RenderService } from './render.service';
import { RenderJob } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { RenderProcessor } from './render.processor';
import { DefaultRenderEngine } from './engines/default-render.engine';
import { RenderJobListener } from './listeners/render-job.listener';
import { RenderCleanupScheduler } from './schedulers/render-cleanup.scheduler';

@Module({
  imports: [
    TypeOrmModule.forFeature([RenderJob, RenderResult]),
    BullModule.registerQueue({
      name: 'render',
    }),
    // ScheduleModule.forRoot(), // 暂时注释掉
    // EventEmitterModule.forRoot(), // 暂时注释掉
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'PROJECT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('PROJECT_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('PROJECT_SERVICE_PORT', 3002),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'ASSET_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('ASSET_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('ASSET_SERVICE_PORT', 3003),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [RenderController],
  providers: [
    RenderService,
    RenderProcessor,
    DefaultRenderEngine,
    RenderJobListener,
    RenderCleanupScheduler,
  ],
})
export class RenderModule {}
