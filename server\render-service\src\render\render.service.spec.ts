/**
 * 渲染服务测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getQueueToken } from '@nestjs/bull';
import { Repository } from 'typeorm';
import { Queue } from 'bull';
import { RenderService } from './render.service';
import { RenderJob, RenderJobStatus, RenderJobType } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { CreateRenderJobDto } from './dto/create-render-job.dto';

describe('RenderService', () => {
  let service: RenderService;
  let renderJobRepository: Repository<RenderJob>;
  let renderResultRepository: Repository<RenderResult>;
  let renderQueue: Queue;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(),
    remove: jest.fn(),
  };

  const mockQueue = {
    add: jest.fn(),
    getJobs: jest.fn(),
  };

  const mockUserService = {
    send: jest.fn(),
  };

  const mockProjectService = {
    send: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RenderService,
        {
          provide: getRepositoryToken(RenderJob),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(RenderResult),
          useValue: mockRepository,
        },
        {
          provide: getQueueToken('render'),
          useValue: mockQueue,
        },
        {
          provide: 'USER_SERVICE',
          useValue: mockUserService,
        },
        {
          provide: 'PROJECT_SERVICE',
          useValue: mockProjectService,
        },
      ],
    }).compile();

    service = module.get<RenderService>(RenderService);
    renderJobRepository = module.get<Repository<RenderJob>>(getRepositoryToken(RenderJob));
    renderResultRepository = module.get<Repository<RenderResult>>(getRepositoryToken(RenderResult));
    renderQueue = module.get<Queue>(getQueueToken('render'));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a render job', async () => {
      const userId = 'user-123';
      const createRenderJobDto: CreateRenderJobDto = {
        name: '测试渲染',
        type: RenderJobType.IMAGE,
        projectId: 'project-123',
        sceneId: 'scene-123',
        settings: {
          width: 1920,
          height: 1080,
          quality: 80,
          format: 'png',
        },
      };

      const mockJob = {
        id: 'job-123',
        ...createRenderJobDto,
        userId,
        status: RenderJobStatus.PENDING,
        progress: 0,
      };

      mockUserService.send.mockReturnValue({ subscribe: (fn) => fn({ id: userId }) });
      mockProjectService.send.mockReturnValue({ subscribe: (fn) => fn(true) });
      mockRepository.create.mockReturnValue(mockJob);
      mockRepository.save.mockResolvedValue(mockJob);
      mockRepository.findOne.mockResolvedValue({ ...mockJob, results: [] });
      mockQueue.add.mockResolvedValue({});

      const result = await service.create(userId, createRenderJobDto);

      expect(result).toBeDefined();
      expect(mockRepository.create).toHaveBeenCalledWith({
        ...createRenderJobDto,
        userId,
        status: RenderJobStatus.PENDING,
        progress: 0,
      });
      expect(mockQueue.add).toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return all render jobs for user', async () => {
      const userId = 'user-123';
      const mockJobs = [
        { id: 'job-1', userId, status: RenderJobStatus.COMPLETED },
        { id: 'job-2', userId, status: RenderJobStatus.PENDING },
      ];

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockJobs),
      };

      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.findAll(userId);

      expect(result).toEqual(mockJobs);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('job.userId = :userId', { userId });
    });
  });

  describe('findOne', () => {
    it('should return a render job by id', async () => {
      const jobId = 'job-123';
      const userId = 'user-123';
      const mockJob = { id: jobId, userId, results: [] };

      mockRepository.findOne.mockResolvedValue(mockJob);

      const result = await service.findOne(jobId, userId);

      expect(result).toEqual(mockJob);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: jobId },
        relations: ['results'],
      });
    });

    it('should throw NotFoundException if job not found', async () => {
      const jobId = 'non-existent';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(jobId, userId)).rejects.toThrow('渲染任务ID non-existent 不存在');
    });

    it('should throw ForbiddenException if user does not own job', async () => {
      const jobId = 'job-123';
      const userId = 'user-123';
      const otherUserId = 'user-456';
      const mockJob = { id: jobId, userId: otherUserId, results: [] };

      mockRepository.findOne.mockResolvedValue(mockJob);

      await expect(service.findOne(jobId, userId)).rejects.toThrow('您没有权限访问此渲染任务');
    });
  });

  describe('updateStatus', () => {
    it('should update render job status', async () => {
      const jobId = 'job-123';
      const status = RenderJobStatus.PROCESSING;
      const progress = 50;
      const mockJob = { id: jobId, status: RenderJobStatus.PENDING };

      mockRepository.findOne.mockResolvedValue(mockJob);
      mockRepository.save.mockResolvedValue({ ...mockJob, status, progress });

      const result = await service.updateStatus(jobId, status, progress);

      expect(result.status).toBe(status);
      expect(result.progress).toBe(progress);
      expect(mockRepository.save).toHaveBeenCalled();
    });
  });

  describe('addResult', () => {
    it('should add render result', async () => {
      const jobId = 'job-123';
      const resultData = {
        fileUrl: '/path/to/file.png',
        fileSize: 1024,
        width: 1920,
        height: 1080,
        format: 'png',
      };
      const mockJob = { id: jobId };
      const mockResult = { id: 'result-123', jobId, ...resultData };

      mockRepository.findOne.mockResolvedValue(mockJob);
      mockRepository.create.mockReturnValue(mockResult);
      mockRepository.save.mockResolvedValue(mockResult);

      const result = await service.addResult(jobId, resultData);

      expect(result).toEqual(mockResult);
      expect(mockRepository.create).toHaveBeenCalledWith({
        ...resultData,
        jobId,
      });
    });
  });
});
