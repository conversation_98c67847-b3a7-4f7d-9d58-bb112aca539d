/**
 * 渲染任务清理调度器
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { RenderJob, RenderJobStatus } from '../entities/render-job.entity';
import { RenderResult } from '../entities/render-result.entity';

@Injectable()
export class RenderCleanupScheduler {
  private readonly logger = new Logger(RenderCleanupScheduler.name);

  constructor(
    @InjectRepository(RenderJob)
    private readonly renderJobRepository: Repository<RenderJob>,
    @InjectRepository(RenderResult)
    private readonly renderResultRepository: Repository<RenderResult>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 每天凌晨2点清理过期的渲染任务
   */
  // @Cron(CronExpression.EVERY_DAY_AT_2AM) // 暂时注释掉，因为schedule模块未安装
  async cleanupExpiredJobs() {
    this.logger.log('开始清理过期的渲染任务...');

    try {
      // 清理30天前的已完成任务
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const expiredJobs = await this.renderJobRepository.find({
        where: {
          status: RenderJobStatus.COMPLETED,
          createdAt: LessThan(thirtyDaysAgo),
        },
        relations: ['results'],
      });

      let cleanedJobs = 0;
      let cleanedFiles = 0;

      for (const job of expiredJobs) {
        try {
          // 删除相关文件
          for (const result of job.results) {
            await this.deleteResultFiles(result);
            cleanedFiles++;
          }

          // 删除任务记录
          await this.renderJobRepository.remove(job);
          cleanedJobs++;

          this.logger.debug(`已清理过期任务: ${job.id}`);
        } catch (error) {
          this.logger.error(`清理任务失败: ${job.id}`, error);
        }
      }

      this.logger.log(`清理完成: 删除了 ${cleanedJobs} 个任务和 ${cleanedFiles} 个文件`);
    } catch (error) {
      this.logger.error('清理过期任务失败', error);
    }
  }

  /**
   * 每小时清理失败的任务文件
   */
  // @Cron(CronExpression.EVERY_HOUR) // 暂时注释掉，因为schedule模块未安装
  async cleanupFailedJobFiles() {
    this.logger.log('开始清理失败任务的临时文件...');

    try {
      // 清理1天前的失败任务文件
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      const failedJobs = await this.renderJobRepository.find({
        where: {
          status: RenderJobStatus.FAILED,
          createdAt: LessThan(oneDayAgo),
        },
      });

      let cleanedDirs = 0;

      for (const job of failedJobs) {
        try {
          const jobDir = path.join(
            this.configService.get<string>('RENDER_OUTPUT_DIR', './renders'),
            job.id
          );

          if (fs.existsSync(jobDir)) {
            await this.deleteDirectory(jobDir);
            cleanedDirs++;
            this.logger.debug(`已清理失败任务目录: ${jobDir}`);
          }
        } catch (error) {
          this.logger.error(`清理失败任务目录失败: ${job.id}`, error);
        }
      }

      this.logger.log(`清理完成: 删除了 ${cleanedDirs} 个失败任务目录`);
    } catch (error) {
      this.logger.error('清理失败任务文件失败', error);
    }
  }

  /**
   * 每6小时清理孤立的文件
   */
  // @Cron('0 */6 * * *') // 暂时注释掉，因为schedule模块未安装
  async cleanupOrphanedFiles() {
    this.logger.log('开始清理孤立的渲染文件...');

    try {
      const renderDir = this.configService.get<string>('RENDER_OUTPUT_DIR', './renders');
      
      if (!fs.existsSync(renderDir)) {
        return;
      }

      const subdirs = fs.readdirSync(renderDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      let cleanedDirs = 0;

      for (const dirname of subdirs) {
        try {
          // 检查是否存在对应的任务记录
          const job = await this.renderJobRepository.findOne({
            where: { id: dirname },
          });

          if (!job) {
            // 孤立的目录，删除它
            const dirPath = path.join(renderDir, dirname);
            await this.deleteDirectory(dirPath);
            cleanedDirs++;
            this.logger.debug(`已清理孤立目录: ${dirPath}`);
          }
        } catch (error) {
          this.logger.error(`检查目录失败: ${dirname}`, error);
        }
      }

      this.logger.log(`清理完成: 删除了 ${cleanedDirs} 个孤立目录`);
    } catch (error) {
      this.logger.error('清理孤立文件失败', error);
    }
  }

  /**
   * 删除渲染结果文件
   */
  private async deleteResultFiles(result: RenderResult): Promise<void> {
    const filesToDelete = [result.fileUrl, result.thumbnailUrl].filter(Boolean);

    for (const filePath of filesToDelete) {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          this.logger.debug(`已删除文件: ${filePath}`);
        }
      } catch (error) {
        this.logger.error(`删除文件失败: ${filePath}`, error);
      }
    }
  }

  /**
   * 递归删除目录
   */
  private async deleteDirectory(dirPath: string): Promise<void> {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const files = fs.readdirSync(dirPath);

    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await this.deleteDirectory(filePath);
      } else {
        fs.unlinkSync(filePath);
      }
    }

    fs.rmdirSync(dirPath);
  }

  /**
   * 获取清理统计信息
   */
  async getCleanupStats(): Promise<any> {
    const totalJobs = await this.renderJobRepository.count();
    const completedJobs = await this.renderJobRepository.count({
      where: { status: RenderJobStatus.COMPLETED },
    });
    const failedJobs = await this.renderJobRepository.count({
      where: { status: RenderJobStatus.FAILED },
    });

    const renderDir = this.configService.get<string>('RENDER_OUTPUT_DIR', './renders');
    let totalSize = 0;
    let fileCount = 0;

    if (fs.existsSync(renderDir)) {
      const calculateSize = (dirPath: string) => {
        const files = fs.readdirSync(dirPath);
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const stat = fs.statSync(filePath);
          if (stat.isDirectory()) {
            calculateSize(filePath);
          } else {
            totalSize += stat.size;
            fileCount++;
          }
        }
      };

      try {
        calculateSize(renderDir);
      } catch (error) {
        this.logger.error('计算目录大小失败', error);
      }
    }

    return {
      totalJobs,
      completedJobs,
      failedJobs,
      storage: {
        totalSize,
        fileCount,
        formattedSize: this.formatBytes(totalSize),
      },
    };
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
