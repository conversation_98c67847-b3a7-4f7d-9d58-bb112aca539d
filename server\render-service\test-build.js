/**
 * 构建测试脚本
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 开始构建测试...');

try {
  // 检查必要文件是否存在
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'src/main.ts',
    'src/app.module.ts',
    'src/types/multer.d.ts'
  ];

  console.log('📋 检查必要文件...');
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`缺少必要文件: ${file}`);
    }
    console.log(`✅ ${file}`);
  }

  // 检查依赖是否安装
  console.log('📦 检查依赖...');
  if (!fs.existsSync('node_modules')) {
    console.log('⚠️  node_modules 不存在，正在安装依赖...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // 尝试构建
  console.log('🔨 开始构建...');
  execSync('npm run build', { stdio: 'inherit' });

  console.log('✅ 构建成功！');
  
  // 检查构建输出
  if (fs.existsSync('dist')) {
    console.log('📁 构建输出目录已创建');
    const distFiles = fs.readdirSync('dist');
    console.log('📄 构建文件:', distFiles.join(', '));
  }

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
