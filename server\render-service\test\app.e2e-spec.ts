/**
 * 端到端测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('RenderService (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('name', '渲染服务');
        expect(res.body).toHaveProperty('version', '1.0.0');
      });
  });

  it('/api/health (GET)', () => {
    return request(app.getHttpServer())
      .get('/api/health')
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('status', 'ok');
      });
  });

  describe('/api/render/jobs', () => {
    const mockToken = 'Bearer mock-jwt-token';

    it('should require authentication', () => {
      return request(app.getHttpServer())
        .post('/api/render/jobs')
        .send({
          name: '测试渲染',
          projectId: 'project-123',
          sceneId: 'scene-123',
          settings: {
            width: 1920,
            height: 1080,
            quality: 80,
            format: 'png',
          },
        })
        .expect(401);
    });

    it('should validate request body', () => {
      return request(app.getHttpServer())
        .post('/api/render/jobs')
        .set('Authorization', mockToken)
        .send({
          // 缺少必需字段
        })
        .expect(400);
    });
  });
});
